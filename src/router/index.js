import {createWebHistory, createRouter} from 'vue-router'
import { useUserStore } from '../store/modules/user'

export const routes = [
    {
        path: '/login',
        name: 'Login',
        component: () => import('../views/Login.vue')
    },
    {
        path: '/export-preview',
        name: 'ExportPreview',
        component: () => import('../views/ExportPreview.vue'),
    },
    {
        path: '/',
        component: () => import('../components/Layout/MainLayout.vue'),
        redirect: '/dashboard',
        children: [
            {
                path: 'dashboard',
                name: 'Dashboard',
                component: () => import('../views/Dashboard.vue'),
            },
            {
                path: 'calculation/energy',
                name: 'EnergyCalculation',
                component: () => import('../views/EnergyCalculation.vue'),
            },
            {
                path: 'calculation/productSolutions',
                name: 'ProductSolutions',
                component: () => import('../views/ProductSolutions.vue'),
            },
            {
                path: 'results/ResultsAnalysis',
                name: 'ResultsAnalysis',
                component: () => import('../views/ResultsAnalysis.vue'),
            },
            {
                path: 'results/CashFlow',
                name: 'CashFlow',
                component: () => import('../views/CashFlow.vue'),
            },
            {
                path: 'results/An',
                name: 'An',
                component: () => import('../views/An.vue'),
            }
        ]
    },
]

const router = createRouter({
    history: createWebHistory(),
    routes: routes,
})

// router.beforeEach(async (to, from, next) => {
//     const userStore = useUserStore();
//
//     if (!userStore.userInfo && userStore.token) {
//         userStore.loadUserFromToken();
//     }
//
//     const isAuthenticated = userStore.isLoggedIn;
//
//     if (to.name !== 'Login' && !isAuthenticated) {
//         next({ name: 'Login' });
//     } else if (to.name === 'Login' && isAuthenticated) {
//         next({ path: '/' });
//     } else {
//         next();
//     }
// });

export default router
