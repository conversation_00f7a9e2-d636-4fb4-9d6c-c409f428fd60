import axios from 'axios';

// 创建 axios 实例
const request = axios.create({
  // aPI 的 base_url
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 5000 // 请求超时时间
});

// 添加请求拦截器
request.interceptors.request.use(request => {
  const token = localStorage.getItem('token'); // 从 localStorage 获取 token
  if (token) {
    request.headers['Authorization'] = `Bearer ${token}`; // 在请求头中添加 token
  }
  return request;
}, error => {
  // 对请求错误做些什么
  return Promise.reject(error);
});

// 添加响应拦截器
request.interceptors.response.use(response => {
  // 对响应数据做点什么 - 直接返回响应
  return response;
}, error => {
  // 统一处理 axios 错误
  let errorMessage = '未知错误';
  
  if (error.response) {
    // 服务器响应了错误状态码
    const status = error.response.status;
    const serverMessage = error.response.data?.message;
    
    if (status === 401) {
      errorMessage = '未授权，请重新登录';
      // 可以在这里清除 token 并跳转到登录页
      localStorage.removeItem('token');
    } else if (status === 403) {
      errorMessage = '权限不足，无法访问';
    } else if (status === 404) {
      errorMessage = '请求的资源不存在';
    } else if (status === 500) {
      errorMessage = '服务器内部错误';
    } else if (serverMessage) {
      errorMessage = serverMessage;
    } else {
      errorMessage = `请求失败，状态码：${status}`;
    }
  } else if (error.request) {
    // 请求已发送但没有收到响应
    errorMessage = '网络错误，请检查网络连接';
  } else {
    // 其他错误
    errorMessage = error.message || '请求配置错误';
  }
  
  // 创建统一的错误对象
  const customError = new Error(errorMessage);
  customError.originalError = error;
  customError.status = error.response?.status;
  
  return Promise.reject(customError);
});

export default request;