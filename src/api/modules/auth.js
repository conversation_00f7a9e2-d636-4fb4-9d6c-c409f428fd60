import request from '../request';

/**
 * 登录API
 * @param {object} data - 包含用户名和密码的对象
 * @returns {Promise}
 */
export function login(data) {
  // 后端需要 x-www-form-urlencoded 格式的数据
  const params = new URLSearchParams();
  params.append('username', data.username);
  params.append('password', data.password);

  return request({
    url: '/auth/login',
    method: 'post',
    data: params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  });
} 