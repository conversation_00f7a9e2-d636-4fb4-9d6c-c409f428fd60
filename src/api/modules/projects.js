import request from "@/api/request.js";

/**
 * 获取项目列表
 * @param {Object} data - 包含分页参数的对象 {skip, limit}
 * @returns {Promise} 请求响应
 */
export function getProjectsList(data = {}) {
  return request({
    url: '/projects',
    method: 'get',
    params: {
      skip: data.skip || 0,
      limit: data.limit || 100
    }
  });
}

/**
 * 根据项目名称获取项目详情
 * @param {Object} data - 包含项目名称的对象 {project_name}
 * @returns {Promise} 请求响应
 */
export function getProjectByName(data) {
  return request({
    url: '/projects/name/' + data.project_name,
    method: 'get'
  });
}

/**
 * 根据项目ID获取项目详情
 * @param {Object} data - 包含项目ID的对象 {project_id}
 * @returns {Promise} 请求响应
 */
export function getProjectById(data) {
  return request({
    url: '/projects/id/' + data.project_id,
    method: 'get'
  });
}

/**
 * 删除项目
 * @param {Object} data - 包含项目ID的对象 {project_id}
 * @returns {Promise} 请求响应
 */
export function deleteProject(data) {
  return request({
    url: '/projects/' + data.project_id,
    method: 'delete'
  });
}
