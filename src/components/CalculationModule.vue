<template>
  <div class="calculation-module">
    <el-card class="module-card">
      <!-- 模块标题 -->
      <template #header>
        <div class="card-header">
          <span class="module-title">{{ title }}</span>
          <el-button v-if="showCalculateBtn" type="primary" @click="handleCalculate" :loading="calculating">
            {{ calculating ? '计算中...' : '运行计算' }}
          </el-button>
        </div>
      </template>

      <div class="module-content">
        <!-- 流程图区域 -->
        <div class="flowchart-section">
          <div class="flowchart-container">
            <img :src="flowchartImage" :alt="title + '流程图'" class="flowchart-image" @error="handleImageError"/>
          </div>
        </div>

        <!-- 数据输入表格 -->
        <div class="data-input-section">
          <h4>参数输入</h4>
          <el-form :model="formData" label-width="120px" class="input-form">
            <el-table :data="tableData" border class="input-table" :max-height="400">
              <el-table-column prop="parameter" label="参数名称" width="200">
                <template #default="{ row }">
                  <span class="parameter-name">{{ row.parameter }}</span>
                  <el-tooltip v-if="row.description" :content="row.description" placement="top">
                    <el-icon class="info-icon">
                      <QuestionFilled/>
                    </el-icon>
                  </el-tooltip>
                </template>
              </el-table-column>

              <el-table-column prop="value" label="数值" width="150">
                <template #default="{ row, $index }">
                  <el-input-number
                      v-model="row.value"
                      :precision="row.precision || 2"
                      :step="row.step || 1"
                      :min="row.min"
                      :max="row.max"
                      size="small"
                      style="width: 100%"
                      @change="handleValueChange($index, row)"
                  />
                </template>
              </el-table-column>

              <el-table-column prop="unit" label="单位" width="100">
                <template #default="{ row }">
                  <span>{{ row.unit }}</span>
                </template>
              </el-table-column>

              <el-table-column prop="category" label="类别" width="120">
                <template #default="{ row }">
                  <el-tag :type="getCategoryType(row.category)" size="small">
                    {{ row.category }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column label="备注" min-width="200">
                <template #default="{ row }">
                  <span class="remark">{{ row.remark }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {computed, ref, watch} from 'vue';
import {QuestionFilled} from '@element-plus/icons-vue';

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  flowchartImage: {
    type: String,
    required: true,
  },
  initialData: {
    type: Array,
    default: () => [],
  },
  showCalculateBtn: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['calculate', 'dataChange']);
const formData = ref({});
const tableData = ref([]);
const calculating = ref(false);

// 初始化表格数据
const initTableData = () => {
  console.log('初始化表格数据:', props.initialData)
  tableData.value = props.initialData.map((item:any) => ({
    parameter: item.parameter,
    value: item.value || 0,
    unit: item.unit || '',
    category: item.category || '基本参数',
    remark: item.remark || '',
    description: item.description || '',
    precision: item.precision || 2,
    step: item.step || 1,
    min: item.min,
    max: item.max,
  }));
};

// 监听初始数据变化
watch(() => props.initialData, initTableData, {immediate: true, deep: true});

// 处理数值变化
const handleValueChange = (index, row) => {
  emit('dataChange', {
    moduleTitle: props.title,
    index,
    row,
    moduleData: tableData.value,
  });
};

// 处理计算按钮点击(单模块)
const handleCalculate = async () => {
  calculating.value = true;
  try {
    const data = tableData.value.map((item) => ({
      parameter: item.parameter,
      value: item.value,
      unit: item.unit,
      category: item.category,
    }));

    await emit('calculate', {
      moduleTitle: props.title,
      data: data,
    });
  } finally {
    calculating.value = false;
  }
};

// 获取类别标签类型
const getCategoryType = (category) => {
  const typeMap = {
    基本参数: 'primary',
    技术参数: 'success',
    经济参数: 'warning',
    环境参数: 'info',
    风险参数: 'danger',
  };
  return typeMap[category] || '';
};

// 处理图片加载错误
const handleImageError = (e) => {
  e.target.src = '/placeholder-flowchart.png'; // 可以设置一个默认的占位图
};

// 暴露方法供父组件调用
defineExpose({
  getData: () => tableData.value,
  setData: (data) => {
    tableData.value = data;
  },
});
</script>

<style scoped>
.calculation-module {
  width: 100%;
  margin-bottom: 30px;
}

.module-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.module-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.module-content {
  display: grid;
  grid-template-columns: 2fr 3fr;
  gap: 20px;
  min-height: 500px;
}

@media (max-width: 1400px) {
  .module-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

.flowchart-section {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 10px;
  background-color: #fafafa;
  min-width: 400px;
}

.flowchart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.flowchart-image {
  max-width: 100%;
  max-height: 400px;
  object-fit: contain;
  border-radius: 4px;
}

.data-input-section h4 {
  margin-bottom: 16px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.input-table {
  width: 100%;
}

.parameter-name {
  font-weight: 500;
}

.info-icon {
  margin-left: 4px;
  color: #909399;
  cursor: help;
}

.remark {
  color: #606266;
  font-size: 12px;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  font-weight: 600;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
