<template>
	<div class="main-layout">
		<el-container>
			<!-- 侧边栏 -->
			<el-aside class="sidebar">
				<div class="logo">
					<img src="/public/images/logo.png" alt="" width="100%" />
				</div>
				<el-menu :default-active="activeMenu" class="sidebar-menu" router>
					<el-menu-item index="/dashboard">
						<el-icon><House /></el-icon>
						<span>首页</span>
					</el-menu-item>
					<el-sub-menu index="/calculation">
						<template #title>
							<el-icon><Coin /></el-icon>
							<span>经济测算</span>
						</template>
						<el-menu-item index="/calculation/energy">
							<span>能源项目测算</span>
						</el-menu-item>
						<el-menu-item index="/calculation/productSolutions">
							<span>产品方案</span>
						</el-menu-item>
					</el-sub-menu>
					<el-sub-menu index="/results">
                        <template #title>
                            <el-icon><PieChart /></el-icon>
                            <span>结果分析</span>
                        </template>
                        <el-menu-item index="/results/ResultsAnalysis">
                            <span>可视化结果分析</span>
						</el-menu-item>
						<el-menu-item index="/results/CashFlow">
							<span>现金流量分析</span>
						</el-menu-item>
						<el-menu-item index="/results/An">
							<span>氨(待完善)</span>
						</el-menu-item>
					</el-sub-menu>
				</el-menu>
			</el-aside>

			<!-- 主内容区域 -->
			<el-container class="right-container">
				<el-header class="header-container" height="64px">
					<Header />
				</el-header>
				<el-main class="main-content">
					<router-view />
				</el-main>
			</el-container>
		</el-container>
	</div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { House, PieChart, Coin } from '@element-plus/icons-vue';
import Header from './Header.vue';

const route = useRoute();
const activeMenu = ref(route.path);

watch(
	() => route.path,
	(newPath) => {
		activeMenu.value = newPath;
	}
);
</script>

<style scoped>
.main-layout {
	height: 100vh;
	display: flex;
	overflow: hidden;
}

.main-layout > .el-container {
	height: 100%;
	display: flex;
}

.right-container {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.sidebar {
	width: 250px;
	background-color: #ffffff;
	height: 100vh;
	overflow-y: auto;
	flex-shrink: 0;
}

.logo {
	background-color: #fff;
	font-size: 18px;
	padding: 6px;
}

.sidebar-menu {
    margin-top: 10px;
	border: none;
	background-color: #ffffff;
	--el-menu-active-color: purple;
}

.header-container {
	padding: 0;
	box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
	flex-shrink: 0;
}

.main-content {
	background-color: #f0f2f5;
	padding: 10px;
	overflow-y: auto;
	flex: 1;
	min-height: 0;
}

/* 响应式适配 */
@media (max-width: 768px) {
	.sidebar {
		width: 200px;
	}
}

@media (max-width: 576px) {
	.main-layout > .el-container {
		flex-direction: column;
	}

	.sidebar {
		width: 100%;
		height: auto;
		max-height: 200px;
		order: 2;
	}

	.right-container {
		order: 1;
		flex: 1;
	}
}
</style>
