<template>
  <div class="header">
    <div class="header-left">
      <div class="header-title">
        <h2>可再生能源制氢合成氨经济性容量配置软件</h2>
      </div>
      
      <!-- 项目选择框 -->
      <div class="project-selection">
        <el-select 
          v-model="selectedProjectId" 
          @change="handleProjectChange"
          placeholder="请选择项目"
          style="width: 240px"
          clearable
        >
          <el-option
            v-for="project in projectHistory"
            :key="project.id"
            :label="project.name"
            :value="project.id"
          >
          </el-option>
        </el-select>
      </div>
      
      <!-- 导出报告功能 -->
      <div class="export-section">
        <el-button @click="openExportPreview" type="primary">
          <el-icon><document /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>
    
    <div class="header-right">
      <!-- 模拟已登录状态，显示默认用户信息 -->
      <el-dropdown trigger="click" @command="handleCommand">
        <div class="avatar-container">
          <el-avatar :size="40">
            用
          </el-avatar>
          <span class="username">演示用户</span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">个人信息</el-dropdown-item>
            <el-dropdown-item command="settings">系统设置</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import { useProjectStore } from '@/store/modules/project'
import { useRouter } from 'vue-router'

const projectStore = useProjectStore()
const router = useRouter()

// 响应式数据
const selectedProjectId = ref(null)

// 计算属性
const currentProject = computed(() => projectStore.currentProject)
const projectHistory = computed(() => projectStore.projectHistory)
// 监听当前项目变化，同步选择框
watch(currentProject, (newProject) => {
  if (newProject) {
    selectedProjectId.value = newProject.id
  } else {
    selectedProjectId.value = null
  }
}, { immediate: true })

// 处理项目选择变化
const handleProjectChange = async (projectId) => {
  if (projectId) {
    try {
      await projectStore.fetchProjectById(projectId)
      ElMessage.success(`已切换到项目：${projectStore.currentProject.name}`)
    } catch (error) {
      ElMessage.error('切换项目失败：' + error.message)
      // 失败时重置选择框
      selectedProjectId.value = currentProject.value?.id || null
    }
  } else {
    // 清空选择
    projectStore.clearCurrentProject()
    ElMessage.info('已清空项目选择')
  }
}

// 打开导出报告预览
const openExportPreview = () => {
  // 获取当前项目ID，如果没有则使用默认值
  const projectId = currentProject.value?.id || 1
  
  // 打开新窗口显示导出报告预览
  const previewUrl = `/export-preview?projectId=${projectId}`
  window.open(previewUrl, '_blank', 'width=1400,height=900,scrollbars=yes,resizable=yes')
}

// 处理下拉菜单命令（简化版，无需登录验证）
const handleCommand = (command) => {
  if (command === 'profile') {
    ElMessage.info('个人信息功能开发中')
  } else if (command === 'settings') {
    ElMessage.info('系统设置功能开发中')
  }
}

// 组件挂载时初始化
onMounted(async () => {
  try {
    // 加载项目列表
    await projectStore.fetchProjectsList()
    // 如果有项目历史记录但没有当前项目，可以选择第一个作为默认项目
    if (projectStore.projectHistory.length > 0 && !currentProject.value) {
      const firstProject = projectStore.projectHistory[0]
      await projectStore.fetchProjectById(firstProject.id)
    }
  } catch (error) {
    ElMessage.error('加载项目列表失败：' + error.message)
  }
})
</script>

<style scoped>
.header {
  height: 64px;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
  color: rgb(117, 66, 165);
  white-space: nowrap;
}

.project-selection {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.export-section {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.header-right {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.avatar-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin-left: 8px;
  font-size: 14px;
}

/* 确保选择框在较小屏幕上的响应性 */
@media (max-width: 1200px) {
  .header-title h2 {
    font-size: 16px;
  }
  
  .project-selection :deep(.el-select) {
    width: 200px !important;
  }
}

@media (max-width: 992px) {
  .header-title h2 {
    font-size: 14px;
  }
  
  .project-selection :deep(.el-select) {
    width: 180px !important;
  }
  
  .header-left {
    gap: 15px;
  }
}
</style> 