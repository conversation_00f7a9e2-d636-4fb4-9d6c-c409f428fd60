import { defineStore } from 'pinia';
import dayjs from 'dayjs';
import { jwtDecode } from 'jwt-decode';

export const useUserStore = defineStore('user', {
    state: () => ({
        token: localStorage.getItem('token') || null,
        userInfo: null
    }),
    getters: {
        isLoggedIn: (state) => {
            if (state.token) {
                try {
                    const payload = jwtDecode(state.token);
                    console.log("计算登录状态，payload：", payload)
                    console.log("当前时间：", dayjs().unix())
                    return dayjs().unix() < payload.exp;
                } catch (e) {
                    return false;
                }
            }
            return false;
        },
    },
    actions: {
        setToken(tokenString) {
            const payload = jwtDecode(tokenString);
            this.userInfo = payload.user_info;
            this.token = tokenString;
            localStorage.setItem('token', tokenString);
        },
        logout() {
            console.log("退出登录")
            this.token = null;
            this.userInfo = null;
            localStorage.removeItem('token');
        },
        loadUserFromToken() {
            if (this.token) {
                try {
                    const payload = jwtDecode(this.token);
                    if (dayjs().unix() < payload.exp) {
                        this.userInfo = payload.user_info;
                    } else {
                        this.logout();
                    }
                } catch (error) {
                    console.error("Invalid token:", error);
                    this.logout();
                }
            }
        }
    }
}); 