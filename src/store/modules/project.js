import { defineStore } from 'pinia';
import { ref } from 'vue';
import { getProjectsList, getProjectByName, getProjectById, deleteProject } from '@/api/modules/projects.js';

export const useProjectStore = defineStore('project', () => {
	// 当前项目信息
	const currentProject = ref(null);

	// 项目历史记录列表
	const projectHistory = ref([]);

	// 加载状态
	const loading = ref(false);

	// 错误信息
	const error = ref(null);

	/**
	 * 获取项目列表
	 * @param {Object} params - 分页参数 {skip, limit}
	 */
	const fetchProjectsList = async (params = {}) => {
		loading.value = true;
		error.value = null;
		try {
			const response = await getProjectsList(params);
			if (response.status === 200) {
				// 提取实际的项目列表数组
				const projectsData = response.data.data || {};
				projectHistory.value = projectsData.projects || [];
				return projectsData;
			} else {
				throw new Error(response.data?.message || '获取项目列表失败');
			}
		} catch (err) {
			error.value = err.message;
			console.error('获取项目列表失败:', err);
			throw err;
		} finally {
			loading.value = false;
		}
	};

	/**
	 * 根据项目ID获取项目详情
	 * @param {number} projectId - 项目ID
	 */
	const fetchProjectById = async (projectId) => {
		loading.value = true;
		error.value = null;
		try {
			const response = await getProjectById({ project_id: projectId });
			if (response.status === 200) {
				currentProject.value = response.data.data;
				return response.data.data;
			} else {
				throw new Error(response.data?.message || '获取项目详情失败');
			}
		} catch (err) {
			error.value = err.message;
			console.error('获取项目详情失败:', err);
			throw err;
		} finally {
			loading.value = false;
		}
	};

	/**
	 * 根据项目名称获取项目详情
	 * @param {string} projectName - 项目名称
	 */
	const fetchProjectByName = async (projectName) => {
		loading.value = true;
		error.value = null;
		try {
			const response = await getProjectByName({ project_name: projectName });
			if (response.status === 200) {
				currentProject.value = response.data.data;
				return response.data.data;
			} else {
				throw new Error(response.data?.message || '获取项目详情失败');
			}
		} catch (err) {
			error.value = err.message;
			console.error('获取项目详情失败:', err);
			throw err;
		} finally {
			loading.value = false;
		}
	};

	/**
	 * 删除项目
	 * @param {number} projectId - 项目ID
	 */
	const removeProject = async (projectId) => {
		loading.value = true;
		error.value = null;
		try {
			const response = await deleteProject({ project_id: projectId });
			if (response.status === 200) {
				// 从项目列表中移除已删除的项目
				projectHistory.value = projectHistory.value.filter((project) => project.id !== projectId);
				// 如果当前项目被删除，清空当前项目
				if (currentProject.value && currentProject.value.id === projectId) {
					currentProject.value = null;
				}
				return true;
			} else {
				throw new Error(response.data?.message || '删除项目失败');
			}
		} catch (err) {
			error.value = err.message;
			console.error('删除项目失败:', err);
			throw err;
		} finally {
			loading.value = false;
		}
	};

	/**
	 * 设置当前项目
	 * @param {Object} project - 项目对象
	 */
	const setCurrentProject = (project) => {
		currentProject.value = project;
	};

	/**
	 * 清空当前项目
	 */
	const clearCurrentProject = () => {
		currentProject.value = null;
	};

	/**
	 * 清空错误信息
	 */
	const clearError = () => {
		error.value = null;
	};

	/**
	 * 刷新项目列表
	 */
	const refreshProjectsList = () => {
		return fetchProjectsList();
	};

	return {
		// 状态
		currentProject,
		projectHistory,
		loading,
		error,

		// 方法
		fetchProjectsList,
		fetchProjectById,
		fetchProjectByName,
		removeProject,
		setCurrentProject,
		clearCurrentProject,
		clearError,
		refreshProjectsList,
	};
});
