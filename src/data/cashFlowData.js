import { reactive } from 'vue'

// 现金流量表数据结构
export const dataAnalysisData = reactive({
  // 现金流入数据
  cashInflow: [
    {
      '项目名称': '销售氢气（交通）',
      '合计': '125000.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '5000.00',
      '2': '5200.00',
      '3': '5400.00',
      '4': '5600.00',
      '5': '5800.00',
      '6': '6000.00',
      '7': '6200.00',
      '8': '6400.00',
      '9': '6600.00',
      '10': '6800.00',
      '11': '7000.00',
      '12': '7200.00',
      '13': '7400.00',
      '14': '7600.00',
      '15': '7800.00',
      '16': '8000.00',
      '17': '8200.00',
      '18': '8400.00',
      '19': '8600.00',
      '20': '8800.00',
      '21': '9000.00',
      '22': '9200.00',
      '23': '9400.00',
      '24': '9600.00',
      '25': '9800.00'
    },
    {
      '项目名称': '销售氢气（化工）',
      '合计': '87500.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '3500.00',
      '2': '3600.00',
      '3': '3700.00',
      '4': '3800.00',
      '5': '3900.00',
      '6': '4000.00',
      '7': '4100.00',
      '8': '4200.00',
      '9': '4300.00',
      '10': '4400.00',
      '11': '4500.00',
      '12': '4600.00',
      '13': '4700.00',
      '14': '4800.00',
      '15': '4900.00',
      '16': '5000.00',
      '17': '5100.00',
      '18': '5200.00',
      '19': '5300.00',
      '20': '5400.00',
      '21': '5500.00',
      '22': '5600.00',
      '23': '5700.00',
      '24': '5800.00',
      '25': '5900.00'
    },
    {
      '项目名称': '销售氢气（化工）',
      '合计': '62500.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '2500.00',
      '2': '2600.00',
      '3': '2700.00',
      '4': '2800.00',
      '5': '2900.00',
      '6': '3000.00',
      '7': '3100.00',
      '8': '3200.00',
      '9': '3300.00',
      '10': '3400.00',
      '11': '3500.00',
      '12': '3600.00',
      '13': '3700.00',
      '14': '3800.00',
      '15': '3900.00',
      '16': '4000.00',
      '17': '4100.00',
      '18': '4200.00',
      '19': '4300.00',
      '20': '4400.00',
      '21': '4500.00',
      '22': '4600.00',
      '23': '4700.00',
      '24': '4800.00',
      '25': '4900.00'
    },
    {
      '项目名称': '销售液氨（化工）',
      '合计': '93750.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '3750.00',
      '2': '3850.00',
      '3': '3950.00',
      '4': '4050.00',
      '5': '4150.00',
      '6': '4250.00',
      '7': '4350.00',
      '8': '4450.00',
      '9': '4550.00',
      '10': '4650.00',
      '11': '4750.00',
      '12': '4850.00',
      '13': '4950.00',
      '14': '5050.00',
      '15': '5150.00',
      '16': '5250.00',
      '17': '5350.00',
      '18': '5450.00',
      '19': '5550.00',
      '20': '5650.00',
      '21': '5750.00',
      '22': '5850.00',
      '23': '5950.00',
      '24': '6050.00',
      '25': '6150.00'
    },
    {
      '项目名称': '销售蒸汽（化工）',
      '合计': '31250.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '1250.00',
      '2': '1280.00',
      '3': '1310.00',
      '4': '1340.00',
      '5': '1370.00',
      '6': '1400.00',
      '7': '1430.00',
      '8': '1460.00',
      '9': '1490.00',
      '10': '1520.00',
      '11': '1550.00',
      '12': '1580.00',
      '13': '1610.00',
      '14': '1640.00',
      '15': '1670.00',
      '16': '1700.00',
      '17': '1730.00',
      '18': '1760.00',
      '19': '1790.00',
      '20': '1820.00',
      '21': '1850.00',
      '22': '1880.00',
      '23': '1910.00',
      '24': '1940.00',
      '25': '1970.00'
    },
    {
      '项目名称': '光伏售电',
      '合计': '156250.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '6250.00',
      '2': '6400.00',
      '3': '6550.00',
      '4': '6700.00',
      '5': '6850.00',
      '6': '7000.00',
      '7': '7150.00',
      '8': '7300.00',
      '9': '7450.00',
      '10': '7600.00',
      '11': '7750.00',
      '12': '7900.00',
      '13': '8050.00',
      '14': '8200.00',
      '15': '8350.00',
      '16': '8500.00',
      '17': '8650.00',
      '18': '8800.00',
      '19': '8950.00',
      '20': '9100.00',
      '21': '9250.00',
      '22': '9400.00',
      '23': '9550.00',
      '24': '9700.00',
      '25': '9850.00'
    },
    {
      '项目名称': '风电售电',
      '合计': '218750.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '8750.00',
      '2': '8950.00',
      '3': '9150.00',
      '4': '9350.00',
      '5': '9550.00',
      '6': '9750.00',
      '7': '9950.00',
      '8': '10150.00',
      '9': '10350.00',
      '10': '10550.00',
      '11': '10750.00',
      '12': '10950.00',
      '13': '11150.00',
      '14': '11350.00',
      '15': '11550.00',
      '16': '11750.00',
      '17': '11950.00',
      '18': '12150.00',
      '19': '12350.00',
      '20': '12550.00',
      '21': '12750.00',
      '22': '12950.00',
      '23': '13150.00',
      '24': '13350.00',
      '25': '13550.00'
    },
    {
      '项目名称': '其他收入',
      '合计': '0.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '光伏电站残值（5%）',
      '合计': '0.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '风电站残值（5%）',
      '合计': '0.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '光伏送出线路残值',
      '合计': '0.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '风电送出线路残值',
      '合计': '0.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '升压站残值（5%）',
      '合计': '0.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '降压站残值（5%）',
      '合计': '0.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '电池储能系统残值',
      '合计': '0.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '制氢工厂残值（5%）',
      '合计': '0.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '储氢系统残值（5%）',
      '合计': '0.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '制氨公辅设施残值',
      '合计': '0.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '合成设备残值（5%）',
      '合计': '0.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '其他设备残值（5%）',
      '合计': '0.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '政府直接补贴',
      '合计': '0.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    }
  ],

  // 现金流出数据
  cashOutflow: [
    {
      '项目名称': '光伏电站投资',
      '合计': '200000.00',
      '-2': '100000.00',
      '-1': '100000.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '风电站投资',
      '合计': '110000.00',
      '-2': '55000.00',
      '-1': '55000.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '光伏送出线路投资',
      '合计': '4000.00',
      '-2': '2000.00',
      '-1': '2000.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '风电送出线路投资',
      '合计': '4000.00',
      '-2': '2000.00',
      '-1': '2000.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '升压站投资',
      '合计': '24500.00',
      '-2': '12250.00',
      '-1': '12250.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '降压站投资',
      '合计': '3500.00',
      '-2': '1750.00',
      '-1': '1750.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '电池储能系统投资',
      '合计': '2000.00',
      '-2': '1000.00',
      '-1': '1000.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '制氢工厂投资',
      '合计': '33000.00',
      '-2': '16500.00',
      '-1': '16500.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '储氢系统投资',
      '合计': '3500.00',
      '-2': '1750.00',
      '-1': '1750.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '制氨公辅设施投资',
      '合计': '15000.00',
      '-2': '7500.00',
      '-1': '7500.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '合成设备投资',
      '合计': '0.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '其他设备投资',
      '合计': '0.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    },
    {
      '项目名称': '制氢站外购原材料（化学品）',
      '合计': '678.84',
      '-2': '0.00',
      '-1': '0.00',
      '1': '27.15',
      '2': '27.15',
      '3': '27.15',
      '4': '27.15',
      '5': '27.15',
      '6': '27.15',
      '7': '27.15',
      '8': '27.15',
      '9': '27.15',
      '10': '27.15',
      '11': '27.15',
      '12': '27.15',
      '13': '27.15',
      '14': '27.15',
      '15': '27.15',
      '16': '27.15',
      '17': '27.15',
      '18': '27.15',
      '19': '27.15',
      '20': '27.15',
      '21': '27.15',
      '22': '27.15',
      '23': '27.15',
      '24': '27.15',
      '25': '27.15'
    },
    {
      '项目名称': '制氢站维修费用（2%）',
      '合计': '25750.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '1030.00',
      '2': '1030.00',
      '3': '1030.00',
      '4': '1030.00',
      '5': '1030.00',
      '6': '1030.00',
      '7': '1030.00',
      '8': '1030.00',
      '9': '1030.00',
      '10': '1030.00',
      '11': '1030.00',
      '12': '1030.00',
      '13': '1030.00',
      '14': '1030.00',
      '15': '1030.00',
      '16': '1030.00',
      '17': '1030.00',
      '18': '1030.00',
      '19': '1030.00',
      '20': '1030.00',
      '21': '1030.00',
      '22': '1030.00',
      '23': '1030.00',
      '24': '1030.00',
      '25': '1030.00'
    },
    {
      '项目名称': '制氢站人员支出',
      '合计': '5000.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '200.00',
      '2': '200.00',
      '3': '200.00',
      '4': '200.00',
      '5': '200.00',
      '6': '200.00',
      '7': '200.00',
      '8': '200.00',
      '9': '200.00',
      '10': '200.00',
      '11': '200.00',
      '12': '200.00',
      '13': '200.00',
      '14': '200.00',
      '15': '200.00',
      '16': '200.00',
      '17': '200.00',
      '18': '200.00',
      '19': '200.00',
      '20': '200.00',
      '21': '200.00',
      '22': '200.00',
      '23': '200.00',
      '24': '200.00',
      '25': '200.00'
    },
    {
      '项目名称': '制氢站其他运营管理支出',
      '合计': '5000.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '200.00',
      '2': '200.00',
      '3': '200.00',
      '4': '200.00',
      '5': '200.00',
      '6': '200.00',
      '7': '200.00',
      '8': '200.00',
      '9': '200.00',
      '10': '200.00',
      '11': '200.00',
      '12': '200.00',
      '13': '200.00',
      '14': '200.00',
      '15': '200.00',
      '16': '200.00',
      '17': '200.00',
      '18': '200.00',
      '19': '200.00',
      '20': '200.00',
      '21': '200.00',
      '22': '200.00',
      '23': '200.00',
      '24': '200.00',
      '25': '200.00'
    },
    {
      '项目名称': '制氢水费',
      '合计': '4525.59',
      '-2': '0.00',
      '-1': '0.00',
      '1': '181.02',
      '2': '181.02',
      '3': '181.02',
      '4': '181.02',
      '5': '181.02',
      '6': '181.02',
      '7': '181.02',
      '8': '181.02',
      '9': '181.02',
      '10': '181.02',
      '11': '181.02',
      '12': '181.02',
      '13': '181.02',
      '14': '181.02',
      '15': '181.02',
      '16': '181.02',
      '17': '181.02',
      '18': '181.02',
      '19': '181.02',
      '20': '181.02',
      '21': '181.02',
      '22': '181.02',
      '23': '181.02',
      '24': '181.02',
      '25': '181.02'
    },
    {
      '项目名称': '制氢排污水费',
      '合计': '2715.36',
      '-2': '0.00',
      '-1': '0.00',
      '1': '108.61',
      '2': '108.61',
      '3': '108.61',
      '4': '108.61',
      '5': '108.61',
      '6': '108.61',
      '7': '108.61',
      '8': '108.61',
      '9': '108.61',
      '10': '108.61',
      '11': '108.61',
      '12': '108.61',
      '13': '108.61',
      '14': '108.61',
      '15': '108.61',
      '16': '108.61',
      '17': '108.61',
      '18': '108.61',
      '19': '108.61',
      '20': '108.61',
      '21': '108.61',
      '22': '108.61',
      '23': '108.61',
      '24': '108.61',
      '25': '108.61'
    },
    {
      '项目名称': '制氢站保险',
      '合计': '2575.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '103.00',
      '2': '103.00',
      '3': '103.00',
      '4': '103.00',
      '5': '103.00',
      '6': '103.00',
      '7': '103.00',
      '8': '103.00',
      '9': '103.00',
      '10': '103.00',
      '11': '103.00',
      '12': '103.00',
      '13': '103.00',
      '14': '103.00',
      '15': '103.00',
      '16': '103.00',
      '17': '103.00',
      '18': '103.00',
      '19': '103.00',
      '20': '103.00',
      '21': '103.00',
      '22': '103.00',
      '23': '103.00',
      '24': '103.00',
      '25': '103.00'
    },
    {
      '项目名称': '光伏运营、管理成本',
      '合计': '11271.43',
      '-2': '0.00',
      '-1': '0.00',
      '1': '450.86',
      '2': '450.86',
      '3': '450.86',
      '4': '450.86',
      '5': '450.86',
      '6': '450.86',
      '7': '450.86',
      '8': '450.86',
      '9': '450.86',
      '10': '450.86',
      '11': '450.86',
      '12': '450.86',
      '13': '450.86',
      '14': '450.86',
      '15': '450.86',
      '16': '450.86',
      '17': '450.86',
      '18': '450.86',
      '19': '450.86',
      '20': '450.86',
      '21': '450.86',
      '22': '450.86',
      '23': '450.86',
      '24': '450.86',
      '25': '450.86'
    },
    {
      '项目名称': '光伏保险费用',
      '合计': '5635.71',
      '-2': '0.00',
      '-1': '0.00',
      '1': '225.43',
      '2': '225.43',
      '3': '225.43',
      '4': '225.43',
      '5': '225.43',
      '6': '225.43',
      '7': '225.43',
      '8': '225.43',
      '9': '225.43',
      '10': '225.43',
      '11': '225.43',
      '12': '225.43',
      '13': '225.43',
      '14': '225.43',
      '15': '225.43',
      '16': '225.43',
      '17': '225.43',
      '18': '225.43',
      '19': '225.43',
      '20': '225.43',
      '21': '225.43',
      '22': '225.43',
      '23': '225.43',
      '24': '225.43',
      '25': '225.43'
    },
    {
      '项目名称': '光伏财务成本',
      '合计': '1080.14',
      '-2': '0.00',
      '-1': '0.00',
      '1': '43.21',
      '2': '43.21',
      '3': '43.21',
      '4': '43.21',
      '5': '43.21',
      '6': '43.21',
      '7': '43.21',
      '8': '43.21',
      '9': '43.21',
      '10': '43.21',
      '11': '43.21',
      '12': '43.21',
      '13': '43.21',
      '14': '43.21',
      '15': '43.21',
      '16': '43.21',
      '17': '43.21',
      '18': '43.21',
      '19': '43.21',
      '20': '43.21',
      '21': '43.21',
      '22': '43.21',
      '23': '43.21',
      '24': '43.21',
      '25': '43.21'
    },
    {
      '项目名称': '风电运营、管理成本',
      '合计': '6128.57',
      '-2': '0.00',
      '-1': '0.00',
      '1': '245.14',
      '2': '245.14',
      '3': '245.14',
      '4': '245.14',
      '5': '245.14',
      '6': '245.14',
      '7': '245.14',
      '8': '245.14',
      '9': '245.14',
      '10': '245.14',
      '11': '245.14',
      '12': '245.14',
      '13': '245.14',
      '14': '245.14',
      '15': '245.14',
      '16': '245.14',
      '17': '245.14',
      '18': '245.14',
      '19': '245.14',
      '20': '245.14',
      '21': '245.14',
      '22': '245.14',
      '23': '245.14',
      '24': '245.14',
      '25': '245.14'
    },
    {
      '项目名称': '风电保险费用',
      '合计': '3064.29',
      '-2': '0.00',
      '-1': '0.00',
      '1': '122.57',
      '2': '122.57',
      '3': '122.57',
      '4': '122.57',
      '5': '122.57',
      '6': '122.57',
      '7': '122.57',
      '8': '122.57',
      '9': '122.57',
      '10': '122.57',
      '11': '122.57',
      '12': '122.57',
      '13': '122.57',
      '14': '122.57',
      '15': '122.57',
      '16': '122.57',
      '17': '122.57',
      '18': '122.57',
      '19': '122.57',
      '20': '122.57',
      '21': '122.57',
      '22': '122.57',
      '23': '122.57',
      '24': '122.57',
      '25': '122.57'
    },
    {
      '项目名称': '风电财务成本',
      '合计': '797.78',
      '-2': '0.00',
      '-1': '0.00',
      '1': '31.91',
      '2': '31.91',
      '3': '31.91',
      '4': '31.91',
      '5': '31.91',
      '6': '31.91',
      '7': '31.91',
      '8': '31.91',
      '9': '31.91',
      '10': '31.91',
      '11': '31.91',
      '12': '31.91',
      '13': '31.91',
      '14': '31.91',
      '15': '31.91',
      '16': '31.91',
      '17': '31.91',
      '18': '31.91',
      '19': '31.91',
      '20': '31.91',
      '21': '31.91',
      '22': '31.91',
      '23': '31.91',
      '24': '31.91',
      '25': '31.91'
    }
  ],

  // 净现金流数据（税务计算）
  NetCashFlow: [
    {
      '项目名称': '所得税前净现金流量（万元）',
      '合计': '262633.54',
      '-2': '-399500.00',
      '-1': '0.00',
      '1': '29005.16',
      '2': '28342.51',
      '3': '27960.97',
      '4': '27586.06',
      '5': '27217.59',
      '6': '26855.38',
      '7': '26499.39',
      '8': '26149.58',
      '9': '25805.92',
      '10': '25468.37',
      '11': '25136.89',
      '12': '24811.45',
      '13': '24492.01',
      '14': '24178.53',
      '15': '23870.99',
      '16': '23569.34',
      '17': '23273.55',
      '18': '22983.58',
      '19': '22699.41',
      '20': '22421.00',
      '21': '22148.31',
      '22': '21881.32',
      '23': '21619.99',
      '24': '21364.30',
      '25': '21114.21'
    },
    {
      '项目名称': '所得税前累计净现金流量（万元）',
      '合计': '-1761617.87',
      '-2': '-399500.00',
      '-1': '-399500.00',
      '1': '-370494.84',
      '2': '-342152.33',
      '3': '-314191.36',
      '4': '-286605.30',
      '5': '-259387.72',
      '6': '-232532.34',
      '7': '-206032.95',
      '8': '-179883.37',
      '9': '-154077.45',
      '10': '-128609.08',
      '11': '-103472.19',
      '12': '-78660.74',
      '13': '-54168.73',
      '14': '-29990.20',
      '15': '-6119.21',
      '16': '17450.13',
      '17': '40723.68',
      '18': '63707.26',
      '19': '86406.67',
      '20': '108827.67',
      '21': '130975.98',
      '22': '152857.30',
      '23': '174477.29',
      '24': '195841.59',
      '25': '216955.80'
    },
    {
      '项目名称': '折旧（万元）',
      '合计': '379525.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '15813.54',
      '2': '15813.54',
      '3': '15813.54',
      '4': '15813.54',
      '5': '15813.54',
      '6': '15813.54',
      '7': '15813.54',
      '8': '15813.54',
      '9': '15813.54',
      '10': '15813.54',
      '11': '15813.54',
      '12': '15813.54',
      '13': '15813.54',
      '14': '15813.54',
      '15': '15813.54',
      '16': '15813.54',
      '17': '15813.54',
      '18': '15813.54',
      '19': '15813.54',
      '20': '15813.54',
      '21': '15813.54',
      '22': '15813.54',
      '23': '15813.54',
      '24': '15813.54',
      '25': '15813.54'
    },
    {
      '项目名称': '增值税销项（售气告气补贴按13%）',
      '合计': '84713.55',
      '-2': '0.00',
      '-1': '0.00',
      '1': '3683.92',
      '2': '3606.59',
      '3': '3562.05',
      '4': '3518.29',
      '5': '3475.28',
      '6': '3433.00',
      '7': '3391.44',
      '8': '3350.59',
      '9': '3310.43',
      '10': '3270.96',
      '11': '3232.16',
      '12': '3194.02',
      '13': '3156.53',
      '14': '3119.68',
      '15': '3083.45',
      '16': '3047.85',
      '17': '3012.86',
      '18': '2978.47',
      '19': '2944.67',
      '20': '2911.46',
      '21': '2878.82',
      '22': '2846.75',
      '23': '2815.24',
      '24': '2784.28',
      '25': '2753.87'
    },
    {
      '项目名称': '增值税进项（一）（运行成本按13%）',
      '合计': '8538.90',
      '-2': '0.00',
      '-1': '0.00',
      '1': '347.04',
      '2': '345.95',
      '3': '345.30',
      '4': '344.67',
      '5': '344.05',
      '6': '343.44',
      '7': '342.84',
      '8': '342.25',
      '9': '341.67',
      '10': '341.10',
      '11': '340.54',
      '12': '339.99',
      '13': '339.45',
      '14': '338.92',
      '15': '338.40',
      '16': '337.89',
      '17': '337.39',
      '18': '336.90',
      '19': '336.42',
      '20': '335.95',
      '21': '335.49',
      '22': '335.04',
      '23': '334.60',
      '24': '334.17',
      '25': '333.75'
    },
    {
      '项目名称': '增值税进项（二）（固定资产按9%）',
      '合计': '0.00',
      '-2': '0.00',
      '-1': '0.00',
      '1': '1305.71',
      '2': '1305.71',
      '3': '1305.71',
      '4': '1305.71',
      '5': '1305.71',
      '6': '1305.71',
      '7': '1305.71',
      '8': '1305.71',
      '9': '1305.71',
      '10': '1305.71',
      '11': '1305.71',
      '12': '1305.71',
      '13': '1305.71',
      '14': '1305.71',
      '15': '1305.71',
      '16': '1305.71',
      '17': '1305.71',
      '18': '1305.71',
      '19': '1305.71',
      '20': '1305.71',
      '21': '1305.71',
      '22': '1305.71',
      '23': '1305.71',
      '24': '1305.71',
      '25': '1305.71'
    },
    {
      '项目名称': '缴纳增值税',
      '合计': '44837.73',
      '-2': '0.00',
      '-1': '0.00',
      '1': '2031.17',
      '2': '1954.94',
      '3': '1911.04',
      '4': '1867.91',
      '5': '1825.52',
      '6': '1783.85',
      '7': '1742.89',
      '8': '1702.63',
      '9': '1663.05',
      '10': '1624.15',
      '11': '1585.91',
      '12': '1548.32',
      '13': '1511.37',
      '14': '1475.05',
      '15': '1439.34',
      '16': '1404.25',
      '17': '1369.76',
      '18': '1335.86',
      '19': '1302.54',
      '20': '1269.80',
      '21': '1237.62',
      '22': '1206.00',
      '23': '1174.93',
      '24': '1144.40',
      '25': '1114.41'
    },
    {
      '项目名称': '缴纳增值税附加',
      '合计': '5380.53',
      '-2': '0.00',
      '-1': '0.00',
      '1': '243.74',
      '2': '234.59',
      '3': '229.33',
      '4': '224.15',
      '5': '219.06',
      '6': '214.06',
      '7': '209.15',
      '8': '204.32',
      '9': '199.57',
      '10': '194.90',
      '11': '190.31',
      '12': '185.80',
      '13': '181.36',
      '14': '177.00',
      '15': '172.72',
      '16': '168.51',
      '17': '164.37',
      '18': '160.30',
      '19': '156.31',
      '20': '152.38',
      '21': '148.51',
      '22': '144.72',
      '23': '140.99',
      '24': '137.33',
      '25': '133.73'
    },
    {
      '项目名称': '增值税及附加总额',
      '合计': '50218.26',
      '-2': '0.00',
      '-1': '0.00',
      '1': '2274.91',
      '2': '2189.53',
      '3': '2140.37',
      '4': '2092.06',
      '5': '2044.58',
      '6': '1997.91',
      '7': '1952.04',
      '8': '1906.95',
      '9': '1862.62',
      '10': '1819.05',
      '11': '1776.22',
      '12': '1734.12',
      '13': '1692.73',
      '14': '1652.05',
      '15': '1612.06',
      '16': '1572.76',
      '17': '1534.13',
      '18': '1496.16',
      '19': '1458.85',
      '20': '1422.18',
      '21': '1386.13',
      '22': '1350.72',
      '23': '1315.92',
      '24': '1281.73',
      '25': '1248.14'
    },
    {
      '项目名称': '所得税（三免三减半）',
      '合计': '37417.03',
      '-2': '0.00',
      '-1': '0.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '968.05',
      '5': '935.95',
      '6': '904.39',
      '7': '873.36',
      '8': '842.85',
      '9': '812.85',
      '10': '783.35',
      '11': '754.35',
      '12': '725.83',
      '13': '697.80',
      '14': '670.24',
      '15': '643.15',
      '16': '616.52',
      '17': '590.34',
      '18': '564.60',
      '19': '539.30',
      '20': '514.43',
      '21': '489.98',
      '22': '465.95',
      '23': '442.33',
      '24': '419.12',
      '25': '396.31'
    },
    {
      '项目名称': '税后净现金流（万元）',
      '合计': '175241.99',
      '-2': '-399500.00',
      '-1': '0.00',
      '1': '26973.99',
      '2': '26152.98',
      '3': '25820.60',
      '4': '25525.95',
      '5': '24237.06',
      '6': '23953.07',
      '7': '23674.19',
      '8': '23400.38',
      '9': '23131.60',
      '10': '22867.82',
      '11': '22609.01',
      '12': '22355.14',
      '13': '22106.18',
      '14': '21862.11',
      '15': '21622.90',
      '16': '21388.52',
      '17': '21158.94',
      '18': '20934.14',
      '19': '20714.09',
      '20': '20498.77',
      '21': '20288.15',
      '22': '20082.21',
      '23': '19880.92',
      '24': '19684.26',
      '25': '19492.21'
    },
    {
      '项目名称': '税后累计现金流（万元）',
      '合计': '-2756648.13',
      '-2': '-399500.00',
      '-1': '-399500.00',
      '1': '-372526.01',
      '2': '-346373.03',
      '3': '-320552.43',
      '4': '-296026.48',
      '5': '-271789.42',
      '6': '-247836.35',
      '7': '-224162.16',
      '8': '-200761.78',
      '9': '-177630.18',
      '10': '-154762.36',
      '11': '-132153.35',
      '12': '-109798.21',
      '13': '-87692.03',
      '14': '-65829.92',
      '15': '-44207.02',
      '16': '-22818.50',
      '17': '-1659.56',
      '18': '19274.58',
      '19': '39988.67',
      '20': '60487.44',
      '21': '80775.59',
      '22': '100857.80',
      '23': '120738.72',
      '24': '140422.98',
      '25': '159915.19'
    },
    {
      '项目名称': '税前财务净现值',
      '合计': '262633.54',
      '-2': '',
      '-1': '',
      '1': '',
      '2': '',
      '3': '',
      '4': '',
      '5': '',
      '6': '',
      '7': '',
      '8': '',
      '9': '',
      '10': '',
      '11': '',
      '12': '',
      '13': '',
      '14': '',
      '15': '',
      '16': '',
      '17': '',
      '18': '',
      '19': '',
      '20': '',
      '21': '',
      '22': '',
      '23': '',
      '24': '',
      '25': ''
    },
    {
      '项目名称': '税前内部收益率',
      '合计': '4.369%',
      '-2': '',
      '-1': '',
      '1': '',
      '2': '',
      '3': '',
      '4': '',
      '5': '',
      '6': '',
      '7': '',
      '8': '',
      '9': '',
      '10': '',
      '11': '',
      '12': '',
      '13': '',
      '14': '',
      '15': '',
      '16': '',
      '17': '',
      '18': '',
      '19': '',
      '20': '',
      '21': '',
      '22': '',
      '23': '',
      '24': '',
      '25': ''
    },
    {
      '项目名称': '税后财务净现值',
      '合计': '-2756648.13',
      '-2': '',
      '-1': '',
      '1': '',
      '2': '',
      '3': '',
      '4': '',
      '5': '',
      '6': '',
      '7': '',
      '8': '',
      '9': '',
      '10': '',
      '11': '',
      '12': '',
      '13': '',
      '14': '',
      '15': '',
      '16': '',
      '17': '',
      '18': '',
      '19': '',
      '20': '',
      '21': '',
      '22': '',
      '23': '',
      '24': '',
      '25': ''
    },
    {
      '项目名称': '税后内部收益率',
      '合计': '3.104%',
      '-2': '',
      '-1': '',
      '1': '',
      '2': '',
      '3': '',
      '4': '',
      '5': '',
      '6': '',
      '7': '',
      '8': '',
      '9': '',
      '10': '',
      '11': '',
      '12': '',
      '13': '',
      '14': '',
      '15': '',
      '16': '',
      '17': '',
      '18': '',
      '19': '',
      '20': '',
      '21': '',
      '22': '',
      '23': '',
      '24': '',
      '25': ''
    },
    {
      '项目名称': '项目回收期',
      '合计': '18.14',
      '-2': '0.00',
      '-1': '0.00',
      '1': '0.00',
      '2': '0.00',
      '3': '0.00',
      '4': '0.00',
      '5': '0.00',
      '6': '0.00',
      '7': '0.00',
      '8': '0.00',
      '9': '0.00',
      '10': '0.00',
      '11': '0.00',
      '12': '0.00',
      '13': '0.00',
      '14': '0.00',
      '15': '0.00',
      '16': '0.00',
      '17': '0.00',
      '18': '0.00',
      '19': '0.00',
      '20': '0.00',
      '21': '0.00',
      '22': '0.00',
      '23': '0.00',
      '24': '0.00',
      '25': '0.00'
    }
  ]
})

// 数据格式转换函数：将后端年度数据转换为前端表格格式
export const convertBackendDataToTableFormat = (backendData) => {
  try {
    console.log('转换后端数据:', backendData)
    
    if (!backendData || typeof backendData !== 'object') {
      console.warn('后端数据格式无效，使用默认数据')
      return {
        cashInflow: dataAnalysisData.cashInflow,
        cashOutflow: dataAnalysisData.cashOutflow,
        NetCashFlow: dataAnalysisData.NetCashFlow
      }
    }

    const convertedData = {
      cashInflow: [],
      cashOutflow: [],
      NetCashFlow: []
    }

    // 动态处理现金流入数据
    if (backendData.cashInflow && Array.isArray(backendData.cashInflow)) {
      convertedData.cashInflow = processFlexibleTableData(backendData.cashInflow, '现金流入')
    } else {
      convertedData.cashInflow = dataAnalysisData.cashInflow
    }

    // 动态处理现金流出数据
    if (backendData.cashOutflow && Array.isArray(backendData.cashOutflow)) {
      convertedData.cashOutflow = processFlexibleTableData(backendData.cashOutflow, '现金流出')
    } else {
      convertedData.cashOutflow = dataAnalysisData.cashOutflow
    }

    // 动态处理净现金流数据
    if (backendData.NetCashFlow && Array.isArray(backendData.NetCashFlow)) {
      convertedData.NetCashFlow = processFlexibleTableData(backendData.NetCashFlow, '净现金流')
    } else {
      convertedData.NetCashFlow = dataAnalysisData.NetCashFlow
    }

    return convertedData

  } catch (error) {
    console.error('数据转换失败:', error)
    return {
      cashInflow: dataAnalysisData.cashInflow,
      cashOutflow: dataAnalysisData.cashOutflow,
      NetCashFlow: dataAnalysisData.NetCashFlow
    }
  }
}

// 灵活处理表格数据：支持动态行列数量
const processFlexibleTableData = (backendArray, categoryName) => {
  if (!Array.isArray(backendArray) || backendArray.length === 0) {
    return []
  }

  console.log(`处理${categoryName}数据:`, backendArray)

  // 检测最大年份数量（动态列数）
  let maxYears = 25 // 默认25年
  backendArray.forEach(item => {
    if (item.yearData && Array.isArray(item.yearData)) {
      maxYears = Math.max(maxYears, item.yearData.length)
    }
  })

  // 动态生成列标题
  const generateColumnStructure = (maxYears) => {
    const baseColumns = {
      '项目名称': '',
      '合计': '0.00',
      '-2': '0.00', // 建设期第1年
      '-1': '0.00'  // 建设期第2年
    }
    
    // 动态添加运营期年份
    for (let i = 1; i <= maxYears; i++) {
      baseColumns[i.toString()] = '0.00'
    }
    
    return baseColumns
  }

  const columnTemplate = generateColumnStructure(maxYears)
  const processedRows = []

  // 处理每一行数据
  backendArray.forEach(item => {
    const row = { ...columnTemplate }
    
    // 设置项目名称
    row['项目名称'] = item.projectName || item.name || '未知项目'
    
    // 设置合计
    row['合计'] = formatNumber(item.total || 0)
    
    // 设置建设期数据
    if (item.constructionPeriod) {
      row['-2'] = formatNumber(item.constructionPeriod.year1 || 0)
      row['-1'] = formatNumber(item.constructionPeriod.year2 || 0)
    }
    
    // 设置运营期数据（支持动态年份）
    if (item.operatingPeriod && Array.isArray(item.operatingPeriod)) {
      item.operatingPeriod.forEach((value, index) => {
        const yearKey = (index + 1).toString()
        if (yearKey in row) {
          row[yearKey] = formatNumber(value || 0)
        }
      })
    } else if (item.yearData && Array.isArray(item.yearData)) {
      // 兼容其他格式
      item.yearData.forEach((yearItem, index) => {
        const yearKey = (index + 1).toString()
        if (yearKey in row) {
          row[yearKey] = formatNumber(yearItem.value || yearItem || 0)
        }
      })
    }
    
    processedRows.push(row)
  })

  console.log(`${categoryName}处理完成，行数：${processedRows.length}，列数：${Object.keys(columnTemplate).length}`)
  return processedRows
}

// 数字格式化函数
const formatNumber = (value) => {
  if (typeof value === 'number') {
    return value.toFixed(2)
  }
  if (typeof value === 'string') {
    const num = parseFloat(value.replace(/,/g, ''))
    return isNaN(num) ? '0.00' : num.toFixed(2)
  }
  return '0.00'
}

// 辅助函数：检测数据结构变化
export const detectDataStructureChanges = (newData, currentData) => {
  const changes = {
    hasNewRows: false,
    hasNewColumns: false,
    newRowCount: 0,
    newColumnCount: 0,
    changedCategories: []
  }

  if (!newData || !currentData) return changes

  // 检测行数变化
  Object.keys(newData).forEach(category => {
    if (newData[category] && currentData[category]) {
      const newRowCount = newData[category].length
      const currentRowCount = currentData[category].length
      
      if (newRowCount !== currentRowCount) {
        changes.hasNewRows = true
        changes.newRowCount = Math.max(changes.newRowCount, newRowCount)
        changes.changedCategories.push(category)
      }

      // 检测列数变化
      if (newData[category][0] && currentData[category][0]) {
        const newColumnCount = Object.keys(newData[category][0]).length
        const currentColumnCount = Object.keys(currentData[category][0]).length
        
        if (newColumnCount !== currentColumnCount) {
          changes.hasNewColumns = true
          changes.newColumnCount = Math.max(changes.newColumnCount, newColumnCount)
        }
      }
    }
  })

  return changes
}

export default {
  dataAnalysisData,
  convertBackendDataToTableFormat
}
