/**
 * 数据适配器 - 用于转换API返回的数据为前端表格格式
 */

/**
 * 适配API返回的完整项目数据
 * @param {Object} apiResponse - API返回的完整响应数据
 * @returns {Object} 适配后的数据对象
 */
export function adaptFullProjectData(apiResponse) {
  if (!apiResponse || !apiResponse.data) {
    return getDefaultFullData();
  }
  
  const apiData = apiResponse.data;
  
  try {
    return {
      energyData: adaptEnergyDataFromAPI(apiData),
      solutionData: adaptSolutionDataFromAPI(apiData),
      CashFlowData: adaptCashFlowDataFromAPI(apiData),
      projectInfo: apiData.project_info || {}
    };
  } catch (error) {
    console.error('完整项目数据适配失败:', error);
    return getDefaultFullData();
  }
}

/**
 * 适配API返回的能源计算数据
 * @param {Object} apiData - API返回的项目数据
 * @returns {Object} 适配后的能源数据对象
 */
function adaptEnergyDataFromAPI(apiData) {
  const operatingParams = apiData.operating_params || {};
  const fixedInvestments = apiData.fixed_investments || [];
  
  return {
    photovoltaicData: [
      { parameter: '光伏装机容量', value: operatingParams.pv_capacity_mw || 0, unit: 'MW', category: '技术参数', remark: '光伏发电装机容量' },
      { parameter: '光伏利用小时数', value: operatingParams.pv_utilization_hours || 0, unit: 'h', category: '技术参数', remark: '年均光伏利用小时数' },
      { parameter: '首年发电量衰减率', value: operatingParams.pv_degradation_y1_percent || 0, unit: '%', category: '技术参数', remark: '首年发电量衰减率' },
      { parameter: '年均发电量衰减率', value: operatingParams.pv_degradation_ongoing_percent || 0, unit: '%', category: '技术参数', remark: '年均发电量衰减率' },
      { parameter: '系统效率', value: operatingParams.pv_system_efficiency_percent || 0, unit: '%', category: '技术参数', remark: '光伏系统效率' },
      { parameter: '光伏送出线路距离', value: operatingParams.pv_transmission_distance_km || 0, unit: 'km', category: '技术参数', remark: '光伏送出线路距离' },
      { parameter: '设备折旧年限', value: operatingParams.pv_equipment_depreciation_years || 0, unit: '年', category: '经济参数', remark: '设备折旧年限' },
      { parameter: '上网电价（含税）', value: operatingParams.pv_grid_price_tax_included_yuan_per_kwh || 0, unit: '元/kWh', category: '经济参数', remark: '上网电价（含税）' }
    ],
    windPowerData: [
      { parameter: '风电装机容量', value: operatingParams.wind_capacity_mw || 0, unit: 'MW', category: '技术参数', remark: '风力发电装机容量' },
      { parameter: '风电利用小时数', value: operatingParams.wind_utilization_hours || 0, unit: 'h', category: '技术参数', remark: '年均风电利用小时数' },
      { parameter: '系统效率', value: operatingParams.wind_system_efficiency_percent || 0, unit: '%', category: '技术参数', remark: '风电系统效率' },
      { parameter: '风电送出线路距离', value: operatingParams.wind_transmission_distance_km || 0, unit: 'km', category: '技术参数', remark: '风电送出线路距离' },
      { parameter: '设备折旧年限', value: operatingParams.wind_equipment_depreciation_years || 0, unit: '年', category: '经济参数', remark: '设备折旧年限' }
    ],
    gridSystemData: [
      { parameter: '上网电价（含税）', value: operatingParams.grid_price_tax_included_yuan_per_kwh || 0, unit: '元/kWh', category: '经济参数', remark: '电网上网电价' },
      { parameter: '电网输电线路长度', value: operatingParams.grid_transmission_length_km || 0, unit: 'km', category: '技术参数', remark: '电网输电线路长度' },
      { parameter: '电网输电单位成本', value: operatingParams.grid_transmission_unit_cost_wan_yuan_per_km || 0, unit: '万元/km', category: '经济参数', remark: '电网输电单位成本' },
      { parameter: '升压站容量', value: operatingParams.grid_step_up_station_capacity_mva || 0, unit: 'MVA', category: '技术参数', remark: '升压站容量' },
      { parameter: '升压站单位成本', value: operatingParams.grid_step_up_station_unit_cost_wan_yuan_per_mva || 0, unit: '万元/MVA', category: '经济参数', remark: '升压站单位成本' },
      { parameter: '降压站容量', value: operatingParams.grid_step_down_station_capacity_mva || 0, unit: 'MVA', category: '技术参数', remark: '降压站容量' },
      { parameter: '降压站单位成本', value: operatingParams.grid_step_down_station_unit_cost_wan_yuan_per_mva || 0, unit: '万元/MVA', category: '经济参数', remark: '降压站单位成本' }
    ],
    hydrogenPlantData: [
      { parameter: '制氢厂装机容量', value: operatingParams.h2_plant_capacity_mw || 0, unit: 'MW', category: '技术参数', remark: '制氢厂装机容量' },
      { parameter: '制氢耗电量', value: operatingParams.h2_consumption_kwh_per_nm3 || 0, unit: 'kWh/Nm³', category: '技术参数', remark: '制氢耗电量' },
      { parameter: '制氢基础耗电量', value: operatingParams.h2_base_consumption_kwh_per_kg || 0, unit: 'kWh/kg', category: '技术参数', remark: '制氢基础耗电量' },
      { parameter: '设备服役年限', value: operatingParams.h2_equipment_service_years || 0, unit: '年', category: '技术参数', remark: '设备服役年限' },
      { parameter: '年度耗电量增长率', value: operatingParams.h2_consumption_increase_annual || 0, unit: '%', category: '技术参数', remark: '年度耗电量增长率' },
      { parameter: '水价', value: operatingParams.h2_water_price_yuan_per_ton || 0, unit: '元/吨', category: '经济参数', remark: '工业用水价格' },
      { parameter: '废水处理费', value: operatingParams.h2_wastewater_price_yuan_per_ton || 0, unit: '元/吨', category: '经济参数', remark: '废水处理费用' },
      { parameter: '员工数量', value: operatingParams.h2_staff_count || 0, unit: '人', category: '运营参数', remark: '制氢厂员工数量' },
      { parameter: '员工年薪', value: operatingParams.h2_staff_salary_wan_yuan_per_year || 0, unit: '万元/年', category: '经济参数', remark: '员工年薪' },
      { parameter: '氢气交通价格', value: operatingParams.h2_price_transport || 0, unit: '元/kg', category: '经济参数', remark: '氢气交通领域销售价格' },
      { parameter: '氢气化工价格', value: operatingParams.h2_price_chemical || 0, unit: '元/kg', category: '经济参数', remark: '氢气化工领域销售价格' },
      { parameter: '储氢投资', value: operatingParams.h2_storage_investment_wan_yuan || 0, unit: '万元', category: '经济参数', remark: '储氢系统投资' },
      { parameter: '储氢容量', value: operatingParams.h2_storage_capacity_tons || 0, unit: 't', category: '技术参数', remark: '储氢容量' },
      { parameter: '设备折旧年限', value: operatingParams.h2_equipment_depreciation_years || 0, unit: '年', category: '经济参数', remark: '设备折旧年限' },
      { parameter: '氧气价格', value: operatingParams.o2_price_per_ton || 0, unit: '元/吨', category: '经济参数', remark: '氧气销售价格' }
    ],
    financingData: [
      { parameter: '贷款期限', value: operatingParams.loan_term_years || 0, unit: '年', category: '融资方案', remark: '贷款期限' },
      { parameter: '贷款利率', value: (operatingParams.loan_interest_rate || 0) * 100, unit: '%', category: '融资方案', remark: '贷款年利率' },
      { parameter: '土地租金', value: operatingParams.finance_land_rent_wan_yuan_per_year || 0, unit: '万元/年', category: '经济参数', remark: '土地租金' },
      { parameter: '贷款比例', value: (operatingParams.loan_ratio || 0) * 100, unit: '%', category: '融资方案', remark: '贷款比例' },
      { parameter: '贷款总额', value: operatingParams.finance_loan_total_wan_yuan || 0, unit: '万元', category: '融资方案', remark: '贷款总额' }
    ],
    liquidAmmoniaData: [
      { parameter: '氨气价格', value: operatingParams.ammonia_price_yuan_per_ton || 0, unit: '元/吨', category: '经济参数', remark: '氨气销售价格' },
      { parameter: '氨气消耗量', value: operatingParams.ammonia_consumption_tons_per_hour || 0, unit: '吨/小时', category: '技术参数', remark: '氨气消耗量' },
      { parameter: '电化学储能规模', value: operatingParams.electrochemical_energy_storage_scale_mw || 0, unit: 'MW', category: '技术参数', remark: '电化学储能规模' }
    ]
  };
}

/**
 * 适配API返回的产品方案数据（固定投资）
 * @param {Object} apiData - API返回的项目数据
 * @returns {Array} 适配后的产品方案数组
 */
function adaptSolutionDataFromAPI(apiData) {
  const fixedInvestments = apiData.fixed_investments || [];
  
  return fixedInvestments.map(item => ({
    parameter: item.item_name || '',
    value: item.total_investment || 0,
    unit: '万元',
    category: item.is_total_row ? '总计' : '成本构成',
    remark: `${item.scale ? item.scale + item.scale_unit : ''} ${item.unit_cost ? '@' + item.unit_cost + item.unit_cost_unit : ''}`.trim() || '固定投资项目'
  }));
}

/**
 * 适配API返回的现金流数据
 * @param {Object} apiData - API返回的项目数据
 * @returns {Object} 适配后的现金流对象
 */
function adaptCashFlowDataFromAPI(apiData) {
  const CashFlowArray = apiData.cash_flow || [];
  const energyMaterialBalance = apiData.energy_material_balance || [];
  
  // 构建现金流入数据
  const cashInflow = [
    buildCashFlowRow('氢气交通销售收入', CashFlowArray, 'h2_transport_sales_income'),
    buildCashFlowRow('氢气化工销售收入', CashFlowArray, 'h2_chemical_sales_income'),
    buildCashFlowRow('氧气销售收入', CashFlowArray, 'o2_sales_income'),
    buildCashFlowRow('氨气销售收入', CashFlowArray, 'ammonia_sales_income'),
    buildCashFlowRow('蒸汽销售收入', CashFlowArray, 'steam_sales_income'),
    buildCashFlowRow('光伏售电收入', CashFlowArray, 'pv_electricity_sales_income'),
    buildCashFlowRow('风电售电收入', CashFlowArray, 'wind_electricity_sales_income'),
    buildCashFlowRow('其他收入', CashFlowArray, 'other_income'),
    buildCashFlowRow('现金流入合计', CashFlowArray, 'total_cash_inflow')
  ];
  
  // 构建现金流出数据
  const cashOutflow = [
    buildCashFlowRow('光伏电站投资', CashFlowArray, 'pv_station_investment'),
    buildCashFlowRow('风电站投资', CashFlowArray, 'wind_station_investment'),
    buildCashFlowRow('制氢厂投资', CashFlowArray, 'h2_plant_investment'),
    buildCashFlowRow('储氢系统投资', CashFlowArray, 'h2_storage_investment'),
    buildCashFlowRow('电池储能投资', CashFlowArray, 'battery_storage_investment'),
    buildCashFlowRow('制氢原材料成本', CashFlowArray, 'h2_raw_materials_cost'),
    buildCashFlowRow('制氢维护成本', CashFlowArray, 'h2_maintenance_cost'),
    buildCashFlowRow('制氢人员成本', CashFlowArray, 'h2_personnel_cost'),
    buildCashFlowRow('制氢水费成本', CashFlowArray, 'h2_water_cost'),
    buildCashFlowRow('光伏运营成本', CashFlowArray, 'pv_operating_cost'),
    buildCashFlowRow('风电运营成本', CashFlowArray, 'wind_operating_cost'),
    buildCashFlowRow('现金流出合计', CashFlowArray, 'total_cash_outflow')
  ];
  
  // 构建税务计算数据
  const NetCashFlow = [
    buildCashFlowRow('税前净现金流', CashFlowArray, 'net_cash_flow_before_tax'),
    buildCashFlowRow('累计税前净现金流', CashFlowArray, 'cumulative_net_cash_flow_before_tax'),
    buildCashFlowRow('折旧', CashFlowArray, 'depreciation'),
    buildCashFlowRow('增值税销项', CashFlowArray, 'vat_output'),
    buildCashFlowRow('增值税进项（运营）', CashFlowArray, 'vat_input_operating'),
    buildCashFlowRow('增值税进项（固定资产）', CashFlowArray, 'vat_input_fixed_assets'),
    buildCashFlowRow('应缴增值税', CashFlowArray, 'vat_payable'),
    buildCashFlowRow('所得税', CashFlowArray, 'income_tax'),
    buildCashFlowRow('税后净现金流', CashFlowArray, 'net_cash_flow_after_tax'),
    buildCashFlowRow('累计税后净现金流', CashFlowArray, 'cumulative_net_cash_flow_after_tax')
  ];
  
  return {
    cashInflow,
    cashOutflow,
    NetCashFlow
  };
}

/**
 * 构建现金流行数据
 * @param {string} projectName - 项目名称
 * @param {Array} CashFlowArray - 现金流数组
 * @param {string} fieldName - 字段名称
 * @returns {Object} 现金流行对象
 */
function buildCashFlowRow(projectName, CashFlowArray, fieldName) {
  const row = {
    '项目名称': projectName,
    '合计': '0.00'
  };
  
  // 初始化所有年份为0
  for (let year = -2; year <= 25; year++) {
    row[year.toString()] = '0.00';
  }
  
  // 填充API数据
  let total = 0;
  CashFlowArray.forEach(item => {
    const year = item.year;
    const value = item[fieldName] || 0;
    if (year >= -2 && year <= 25) {
      row[year.toString()] = formatCurrencyValue(value);
      total += parseFloat(value);
    }
  });
  
  row['合计'] = formatCurrencyValue(total);
  return row;
}

/**
 * 格式化货币值
 * @param {number} value - 数值
 * @returns {string} 格式化后的字符串
 */
function formatCurrencyValue(value) {
  if (typeof value !== 'number') return '0.00';
  return value.toFixed(2);
}

/**
 * 适配能源计算数据（向后兼容）
 * @param {Object} apiData - API返回的能源计算数据
 * @returns {Object} 适配后的能源数据对象
 */
export function adaptEnergyData(apiData) {
  if (!apiData) return getDefaultEnergyData();
  
  try {
    // 假设API返回的数据结构，你需要根据实际API调整
    return {
      photovoltaicData: adaptParameterArray(apiData.photovoltaic || []),
      windPowerData: adaptParameterArray(apiData.windPower || []),
      gridSystemData: adaptParameterArray(apiData.gridSystem || []),
      hydrogenPlantData: adaptParameterArray(apiData.hydrogenPlant || []),
      financingData: adaptParameterArray(apiData.financing || []),
      liquidAmmoniaData: adaptParameterArray(apiData.liquidAmmonia || [])
    };
  } catch (error) {
    console.error('能源数据适配失败:', error);
    return getDefaultEnergyData();
  }
}

/**
 * 适配产品方案数据（向后兼容）
 * @param {Object} apiData - API返回的产品方案数据
 * @returns {Array} 适配后的产品方案数组
 */
export function adaptSolutionData(apiData) {
  if (!apiData) return getDefaultSolutionData();
  
  try {
    // 假设API返回的数据结构，你需要根据实际API调整
    if (Array.isArray(apiData)) {
      return adaptParameterArray(apiData);
    } else if (apiData.solutionParameters) {
      return adaptParameterArray(apiData.solutionParameters);
    }
    
    return getDefaultSolutionData();
  } catch (error) {
    console.error('产品方案数据适配失败:', error);
    return getDefaultSolutionData();
  }
}

/**
 * 适配现金流数据（向后兼容）
 * @param {Object} apiData - API返回的现金流数据
 * @returns {Object} 适配后的现金流对象
 */
export function adaptCashFlowData(apiData) {
  if (!apiData) return getDefaultCashFlowData();
  
  try {
    return {
      cashInflow: adaptCashFlowArray(apiData.cashInflow || []),
      cashOutflow: adaptCashFlowArray(apiData.cashOutflow || []),
      NetCashFlow: adaptCashFlowArray(apiData.NetCashFlow || [])
    };
  } catch (error) {
    console.error('现金流数据适配失败:', error);
    return getDefaultCashFlowData();
  }
}

/**
 * 适配参数数组 - 将API数据转换为前端表格行格式
 * @param {Array} apiArray - API返回的参数数组
 * @returns {Array} 适配后的参数数组
 */
function adaptParameterArray(apiArray) {
  if (!Array.isArray(apiArray)) return [];
  
  return apiArray.map(item => ({
    parameter: item.parameter || item.name || item.parameterName || '',
    value: parseFloat(item.value) || 0,
    unit: item.unit || '',
    category: item.category || item.type || '技术参数',
    remark: item.remark || item.description || item.comment || '',
    // 保留其他可能的属性
    precision: item.precision || 2,
    step: item.step || 1,
    min: item.min || 0,
    max: item.max
  }));
}

/**
 * 适配现金流数组 - 将API数据转换为前端现金流表格格式
 * @param {Array} apiArray - API返回的现金流数组
 * @returns {Array} 适配后的现金流数组
 */
function adaptCashFlowArray(apiArray) {
  if (!Array.isArray(apiArray)) return [];
  
  return apiArray.map(item => {
    const adaptedItem = {
      '项目名称': item.projectName || item.name || item['项目名称'] || '',
      '合计': item.total || item.sum || item['合计'] || '0.00'
    };
    
    // 适配年份数据（建设期 -2, -1 和运营期 1-25）
    for (let year = -2; year <= 25; year++) {
      const yearKey = year.toString();
      adaptedItem[yearKey] = item[yearKey] || item[`year${year}`] || item[`Y${year}`] || '0.00';
    }
    
    return adaptedItem;
  });
}

/**
 * 获取默认能源数据
 */
function getDefaultEnergyData() {
  return {
    photovoltaicData: [
      { parameter: '光伏装机容量', value: 1000, unit: 'MW', category: '技术参数', remark: '光伏电站装机规模' },
      { parameter: '年平均发电小时数', value: 1500, unit: 'h', category: '技术参数', remark: '年等效满负荷发电小时数' },
      { parameter: '初始投资成本', value: 400000, unit: '万元', category: '经济参数', remark: '光伏电站建设投资' },
      { parameter: '运维成本', value: 2000, unit: '万元/年', category: '经济参数', remark: '年度运行维护费用' },
    ],
    windPowerData: [
      { parameter: '风电装机容量', value: 500, unit: 'MW', category: '技术参数', remark: '风力发电装机规模' },
      { parameter: '年平均发电小时数', value: 2200, unit: 'h', category: '技术参数', remark: '年等效满负荷发电小时数' },
      { parameter: '初始投资成本', value: 350000, unit: '万元', category: '经济参数', remark: '风电场建设投资' },
      { parameter: '运维成本', value: 3500, unit: '万元/年', category: '经济参数', remark: '年度运行维护费用' },
    ],
    gridSystemData: [
      { parameter: '电网接入容量', value: 1500, unit: 'MW', category: '技术参数', remark: '电网最大接入功率' },
      { parameter: '输电线路长度', value: 150, unit: 'km', category: '技术参数', remark: '输电线路总长度' },
      { parameter: '电网建设成本', value: 80000, unit: '万元', category: '经济参数', remark: '电网基础设施投资' },
    ],
    hydrogenPlantData: [
      { parameter: '制氢装置规模', value: 100, unit: 'MW', category: '技术参数', remark: '电解制氢装置功率' },
      { parameter: '年制氢量', value: 1750, unit: '万Nm³', category: '技术参数', remark: '年度氢气产量' },
      { parameter: '制氢投资成本', value: 120000, unit: '万元', category: '经济参数', remark: '制氢设备投资' },
    ],
    financingData: [
      { parameter: '项目总投资', value: 950000, unit: '万元', category: '投资概算', remark: '项目建设总投资' },
      { parameter: '自有资金比例', value: 30, unit: '%', category: '融资方案', remark: '项目自有资金占比' },
      { parameter: '贷款利率', value: 4.5, unit: '%', category: '融资方案', remark: '银行贷款年利率' },
    ],
    liquidAmmoniaData: [
      { parameter: '合成氨产能', value: 100, unit: '万吨/年', category: '技术参数', remark: '年合成氨生产能力' },
      { parameter: '氨合成投资', value: 180000, unit: '万元', category: '经济参数', remark: '氨合成装置投资' },
      { parameter: '氨气销售价格', value: 3800, unit: '元/吨', category: '经济参数', remark: '液氨市场销售价格' },
    ]
  };
}

/**
 * 获取默认产品方案数据
 */
function getDefaultSolutionData() {
  return [
    { parameter: '合成氨装置规模', value: 100, unit: '万吨/年', category: '容量优化配置结果', remark: '合成氨生产装置年产能规模' },
    { parameter: '氨合成系统规模', value: 25, unit: 'MW', category: '容量优化配置结果', remark: '氨合成系统装机容量' },
    { parameter: '电化学储能规模', value: 25, unit: 'MW', category: '容量优化配置结果', remark: '电化学储能系统装机规模' },
    { parameter: '储氢规模', value: 700, unit: 't', category: '容量优化配置结果', remark: '氢气储存容量规模' },
    { parameter: '年合成氨产量', value: 95, unit: '万吨/年', category: '总体产品方案', remark: '年度合成氨实际产量' },
    { parameter: '年发电总电量', value: 33000, unit: '万kWh', category: '总体产品方案', remark: '年度发电总量' },
    { parameter: '上网电量', value: 15000, unit: '万kWh', category: '总体产品方案', remark: '输送到电网的电量' },
    { parameter: '储氢电量', value: 18000, unit: '万kWh', category: '总体产品方案', remark: '用于储氢的电量' },
    { parameter: '年制氢总量', value: 1750, unit: '万Nm3', category: '总体产品方案', remark: '年度制氢总产量' },
    { parameter: '项目周期', value: 20, unit: '年', category: '系统经济性连算结果', remark: '项目运营周期' },
    { parameter: '全生命周期总利润', value: 280000, unit: '万元', category: '系统经济性连算结果', remark: '项目全生命周期总利润' }
  ];
}

/**
 * 获取默认现金流数据
 */
function getDefaultCashFlowData() {
  // 这里返回简化的默认现金流数据，实际使用时从 CashFlowData.js 导入
  return {
    cashInflow: [],
    cashOutflow: [],
    NetCashFlow: []
  };
}

/**
 * 获取默认完整数据
 */
function getDefaultFullData() {
  return {
    energyData: getDefaultEnergyData(),
    solutionData: getDefaultSolutionData(),
    CashFlowData: getDefaultCashFlowData(),
    projectInfo: {}
  };
}

/**
 * 安全获取数值，有默认值兜底
 */
function safeParseNumber(value, defaultValue = 0) {
  if (value === null || value === undefined || value === '') {
    return defaultValue;
  }
  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
}

/**
 * 安全获取字符串
 */
function safeParseString(value, defaultValue = '') {
  if (value === null || value === undefined) {
    return defaultValue;
  }
  return String(value);
}
