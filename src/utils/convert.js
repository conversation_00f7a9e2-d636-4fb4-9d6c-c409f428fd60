// 辅助函数：将[{parameter: '名称', value: 123}, ...] 转换为 {名称: 123, ...}
const transformArrayToObject = (arr) => {
  return arr.reduce((acc, item) => {
    // 将中文参数名转换为更适合做键名的英文标识符
    // 这一步是可选的，但强烈推荐，可以避免很多问题
    // 您可以维护一个简单的映射表来完成转换
    const key = parameterNameToKey(item.parameter);
    acc[key] = item.value;
    return acc;
  }, {});
};
// 建议的参数名到键名的映射函数
// 这样后端处理的是稳定的英文key，而不是易变的中文标签
function parameterNameToKey(paramName) {
    const map = {
        // 光伏参数模块
        '光伏装机容量（MW）': 'pv_capacity_mw',
        '光伏利用小时数（h)': 'pv_utilization_hours',
        '首年发电量衰减率（%）': 'pv_degradation_y1_percent',
        '年均发电量衰减率（%）': 'pv_degradation_ongoing_percent',
        '系统效率（%）': 'system_efficiency_percent', // 光伏和风电都有，使用通用名称
        '光伏送出线路距离（公里）': 'pv_transmission_distance_km',
        '设备折旧年限（年）': 'equipment_depreciation_years', // 多个模块都有，使用通用名称
        '上网电价(含税)（元/kwh)': 'grid_price_tax_included_yuan_per_kwh', // 光伏和风电都有，使用通用名称

        // 风电参数模块
        '风电装机容量（MW）': 'wind_capacity_mw',
        '风电利用小时数（h)': 'wind_utilization_hours',
        '风电送出线路距离（公里）': 'wind_transmission_distance_km',

        // 电网系统模块
        '送出线路长度': 'grid_transmission_length_km',
        '送出线路单位造价': 'grid_transmission_unit_cost_wan_yuan_per_km',
        '升压站规模': 'grid_step_up_station_capacity_mva',
        '升压站单位造价': 'grid_step_up_station_unit_cost_wan_yuan_per_mva',
        '降压站规模': 'grid_step_down_station_capacity_mva',
        '降压站单位造价': 'grid_step_down_station_unit_cost_wan_yuan_per_mva',

        // 制氢厂参数模块
        '制氢容量（MW)': 'h2_plant_capacity_mw',
        '单位耗电量（kWh/Nm3）': 'h2_consumption_kwh_per_nm3',
        '单位耗电量（kWh/kg）': 'h2_base_consumption_kwh_per_kg',
        '设备使用年限（年）': 'h2_equipment_service_years',
        '制氢设备年化衰减率': 'h2_consumption_increase_annual',
        '水价（元/吨）': 'h2_water_price_yuan_per_ton',
        '排污水价（元/吨）': 'h2_wastewater_price_yuan_per_ton',
        '人员数量（人)': 'h2_staff_count',
        '人员工资（万元/年）': 'h2_staff_salary_wan_yuan_per_year',
        '交通用氢价格（元/kg）': 'h2_price_transport',
        '化工用氢价格（元/kg）': 'h2_price_chemical',
        '氧气价格（元/吨）': 'o2_price_per_ton',
        '储氢投资（万元）': 'h2_storage_investment_wan_yuan',
        '储氢规模（吨）': 'h2_storage_capacity_tons',

        // 融资贷款模块
        '贷款年限（年）': 'loan_term_years',
        '利率（%）': 'loan_interest_rate_percent',
        '制氢厂土地租金（万元/年）': 'finance_land_rent_wan_yuan_per_year',
        '贷款比例': 'loan_ratio',
        '贷款总额（万元）': 'finance_loan_total_wan_yuan',

        // 液氨参数模块
        '液氨价格（元/吨）': 'ammonia_price_yuan_per_ton',
        '液氨用量（吨/小时）': 'ammonia_consumption_tons_per_hour',

        // 储能电站模块
        '电化学储能规模(MW)': 'electrochemical_energy_storage_scale_mw',
    };
    const key = Object.keys(map).find(k => paramName.startsWith(k));
    return map[key] || paramName; // 如果找不到映射，返回原名
}

export { transformArrayToObject, parameterNameToKey };
