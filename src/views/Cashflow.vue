<template>
  <div class="results-page">
    <div class="page-header">
      <h1>现金流量分析</h1>
      <div class="header-actions">
      </div>
    </div>

    <div class="results-content">

      <!-- 现金流量表 -->
      <el-row :gutter="20" class="charts-section">
        <el-col :span="24">
          <el-card>
            <template #header>
              <div class="chart-header">
                <h3>现金流量表</h3>
                <div class="chart-selector">
                  <el-dropdown @command="handleDataAnalysisChange" trigger="click">
                    <el-button type="primary" size="small">
                      {{ getCurrentDataAnalysisLabel() }}
                      <el-icon class="el-icon--right"><arrow-down /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="CashInFlow">现金流入</el-dropdown-item>
                        <el-dropdown-item command="CashOutFlow">现金流出</el-dropdown-item>
                        <el-dropdown-item command="NetCashFlow">税务</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </template>

            <!-- 现金流量表 -->
            <div v-loading="dataAnalysisLoading" element-loading-text="加载数据中...">
              <div class="data-analysis-table-container">
                <el-table
                  :data="currentDataAnalysisData"
                  border
                  stripe
                  class="data-analysis-table"

                  :row-class-name="getRowClassName"
                  :header-cell-style="headerCellStyle"
                >
                  <el-table-column prop="项目名称" label="项目名称" width="200" fixed="left" header-align="center" align="left" />
                  <el-table-column prop="合计" label="合计" width="150" header-align="right" align="right" />
                  <el-table-column label="建设期" align="center" header-align="center">
                    <el-table-column prop="-2" label="-2" width="120" align="center" header-align="center" />
                    <el-table-column prop="-1" label="-1" width="120" align="center" header-align="center" />
                  </el-table-column>
                  <el-table-column label="投产经营期" align="center" header-align="center">
                    <el-table-column
                      v-for="year in yearColumns"
                      :key="`year-${year}`"
                      :prop="year.toString()"
                      :label="year.toString()"
                      width="120"
                      align="center"
                      header-align="center"
                    />
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>


    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getCashFlowData } from '@/api/modules/CashFlow'
import { dataAnalysisData, convertBackendDataToTableFormat } from '@/data/CashFlowData'

// TypeScript 类型定义
interface CashFlowDataRow {
  '项目名称': string
  '合计': string
  '-2': string
  '-1': string
  [key: string]: string // 用于年份字段 '1', '2', ..., '25'
}

interface DetailedResult {
  project: string
  investment: number
  capacity: number
  annualRevenue: number
  irr: number
  npv: number
  paybackPeriod: number
  riskLevel: string
}


const route = useRoute()
const router = useRouter()

// 数据状态
const inputData = ref<any>(null)
const CashFlowType = ref<'cumulative' | 'annual'>('cumulative')

// 现金流量表相关状态
const selectedDataAnalysis = ref<'CashInFlow' | 'CashOutFlow' | 'NetCashFlow'>('CashInFlow')
const dataAnalysisLoading = ref<boolean>(false)

// 现金流量表标签映射
const dataAnalysisLabels: Record<string, string> = {
  CashInFlow: '现金流入',
  CashOutFlow: '现金流出',
  NetCashFlow: '税务'
}

// 数据键映射 - 将UI选择映射到实际数据键
const dataKeyMapping: Record<string, string> = {
  CashInFlow: 'cashInflow',
  CashOutFlow: 'cashOutflow', 
  NetCashFlow: 'NetCashFlow'
}

// 现金流量表数据 - 已迁移到外部文件
// 使用从 src/data/cashFlowData.js 导入的数据

// 页面操作
// 现金流量表相关方法
const getCurrentDataAnalysisLabel = (): string => {
  return dataAnalysisLabels[selectedDataAnalysis.value] || '现金流入'
}

const handleDataAnalysisChange = (command: string): void => {
  selectedDataAnalysis.value = command as 'CashInFlow' | 'CashOutFlow' | 'NetCashFlow'
  ElMessage.success(`已切换到${dataAnalysisLabels[command]}`)
}

// 数据格式转换函数：将后端年度数据转换为前端表格格式
// 已迁移到 @/data/cashFlowData.js 文件中

// 加载现金流量表数据
const loadDataAnalysisData = async (): Promise<void> => {
  try {
    dataAnalysisLoading.value = true

    // 获取项目ID，优先从路由参数获取
    const projectId = route.query.projectId || route.params.projectId || 1

    console.log('开始加载项目数据:', projectId)
    
    // 先显示本地演示数据
    console.log('使用本地演示数据')
    ElMessage.success('现金流量表演示数据加载成功')
    
    // 可选：尝试从API加载数据
    try {
      const response = await getCashFlowData({ projectId })
      console.log("response结果为",response);
      const result = response.data;
      if (result.code === 200 && result.data) {
        // 将后端数据转换为前端表格格式并更新
        const convertedData = convertBackendDataToTableFormat(result.data)

        // 根据后端返回的数据结构更新前端数据
        if (convertedData.cashInflow) {
          dataAnalysisData.cashInflow.splice(0, dataAnalysisData.cashInflow.length, ...convertedData.cashInflow)
        }
        if (convertedData.cashOutflow) {
          dataAnalysisData.cashOutflow.splice(0, dataAnalysisData.cashOutflow.length, ...convertedData.cashOutflow)
        }
        if (convertedData.NetCashFlow) {
          dataAnalysisData.NetCashFlow.splice(0, dataAnalysisData.NetCashFlow.length, ...convertedData.NetCashFlow)
        }

        ElMessage.success('现金流量表API数据加载成功')
      } else {
        console.warn('后端API返回数据格式异常，使用默认数据')
      }
    } catch (apiError) {
      console.warn('API调用失败，使用本地演示数据:', apiError)
    }

  } catch (error) {
    console.error('加载现金流量表数据失败:', error)
    ElMessage.warning('使用本地演示数据')
  } finally {
    dataAnalysisLoading.value = false
  }
}

// 获取当前现金流量表数据
const currentDataAnalysisData = computed((): CashFlowDataRow[] => {
  const dataKey = dataKeyMapping[selectedDataAnalysis.value] || 'cashInflow'
  const data = dataAnalysisData[dataKey] || []
  console.log('当前选择的数据类型:', selectedDataAnalysis.value)
  console.log('实际数据键:', dataKey)
  console.log('当前数据:', data)
  console.log('数据字段:', data.length > 0 ? Object.keys(data[0]) : [])
  return data
})

// 动态生成运营期年份数量
const operatingYears = computed((): number => {
  const data = currentDataAnalysisData.value
  if (data.length === 0) return 25 // 默认25年
  
  const firstRow = data[0]
  const yearKeys = Object.keys(firstRow).filter(key => {
    return /^\d+$/.test(key) && parseInt(key) > 0
  })
  
  const maxYear = yearKeys.length > 0 ? Math.max(...yearKeys.map(k => parseInt(k))) : 25
  console.log('检测到的最大运营年份:', maxYear)
  return maxYear
})

// 动态生成年份列表
const yearColumns = computed((): number[] => {
  const years = []
  for (let i = 1; i <= operatingYears.value; i++) {
    years.push(i)
  }
  return years
})

// 检测数据结构变化
computed(() => {
  const data = currentDataAnalysisData.value
  if (data.length === 0) return { rowCount: 0, columnCount: 0, hasData: false }

  const firstRow = data[0]
  const allKeys = Object.keys(firstRow)
  const yearKeys = allKeys.filter(key => /^-?\d+$/.test(key))

  return {
    rowCount: data.length,
    columnCount: allKeys.length,
    yearColumnCount: yearKeys.length,
    hasData: true,
    isDynamic: operatingYears.value !== 25
  }
});
// 表头单元格样式
const headerCellStyle = ({ column, columnIndex }: { column: any, columnIndex: number }): Record<string, string> => {
  // 根据列的属性和标签来判断对齐方式
  const label = column.label
  const property = column.property

  if (property === '项目名称' || label === '项目名称') {
    return { textAlign: 'center' }
  }

  if (property === '合计' || label === '合计') {
    return { textAlign: 'right' }
  }

  if (label === '建设期' || label === '投产经营期') {
    return { textAlign: 'center' }
  }

  // 对于年份列（包括负数年份和正数年份）
  if (property === '-2' || property === '-1' ||
      (property && /^\d+$/.test(property)) ||
      (label && /^-?\d+$/.test(label))) {
    return { textAlign: 'center' }
  }
  return { textAlign: 'center' }
}

// 获取行的CSS类名
const getRowClassName = ({ row, rowIndex }: { row: any, rowIndex: number }): string => {
  if (row.isSpecialFirst) {
    return 'special-first-row'
  }
  if (row.isSpecialSecond) {
    return 'special-second-row'
  }
  if (row.isSpecialThird) {
    return 'special-third-row'
  }
  if (row.isSpecialFourth) {
    return 'special-fourth-row'
  }
  if (row.isSpecialFifth) {
    return 'special-fifth-row'
  }
  if (row.isParent) {
    return 'parent-row'
  }
  if (row.isChild) {
    return 'child-row'
  }
  return ''
}
// 页面初始化
onMounted((): void => {
  // 调试：打印数据结构
  console.log('dataAnalysisData 对象:', dataAnalysisData)
  console.log('cashInflow 数据:', dataAnalysisData.cashInflow)
  console.log('cashOutflow 数据:', dataAnalysisData.cashOutflow)
  console.log('NetCashFlow 数据:', dataAnalysisData.NetCashFlow)
  
  // 解析传入的数据
  if (route.query.data) {
    try {
      inputData.value = JSON.parse(decodeURIComponent(route.query.data as string))
      console.log('输入数据:', inputData.value)
      // 根据输入数据更新计算结果
      // updateResultsBasedOnInput()
    } catch (error) {
      console.error('解析数据失败:', error)
    }
  }

  // 加载现金流量表数据
  loadDataAnalysisData()

  // 强制修复表头对齐问题
  nextTick(() => {
    fixHeaderAlignment()
  })
})

// 修复表头对齐的函数
const fixHeaderAlignment = (): void => {
  setTimeout(() => {
    const table = document.querySelector('.data-analysis-table')
    if (table) {
      // 获取所有表头单元格
      const headerCells = table.querySelectorAll('.el-table__header th .el-table__cell')

      headerCells.forEach((cell: Element) => {
        const htmlCell = cell as HTMLElement
        const text = htmlCell.textContent?.trim()
        if (text === '项目名称') {
          htmlCell.style.setProperty('text-align', 'center', 'important')
        }
        else if (text === '合计') {
          htmlCell.style.setProperty('text-align', 'right', 'important')
        }
        else if (text === '建设期' || text === '投产经营期') {
          htmlCell.style.setProperty('text-align', 'center', 'important')
        }
        else if (text && /^-?\d+$/.test(text)) {
          htmlCell.style.setProperty('text-align', 'center', 'important')
        }
      })
    }
  }, 500) 
}
</script>

<style scoped>
.results-page {
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.page-header h1 {
  color: #303133;
  margin: 0;
  font-size: 28px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.kpi-overview {
  margin-bottom: 30px;
}

.kpi-card {
  text-align: center;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
}

.kpi-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.kpi-content {
  padding: 20px 0;
}

.kpi-value {
  font-size: 28px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 8px;
}

.kpi-label {
  font-size: 14px;
  color: #606266;
}

.charts-section {
  margin-bottom: 30px;
}

.chart {
  height: 350px;
  width: 100%;
}

.chart-large {
  height: 400px;
  width: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h3 {
  margin: 0;
  color: #303133;
}

.data-info-section {
  margin-bottom: 20px;
}

.api-test-section {
  margin-bottom: 30px;
}

.api-test-section .el-card {
  border: 1px solid #e4e7ed;
}

.api-test-section .el-button {
  margin-left: 8px;
}

.data-table-section {
  margin-top: 30px;
}

:deep(.el-card__header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-card__header h3) {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

/* 现金流量表样式 */
.data-analysis-table-container {
  overflow-x: auto;
  overflow-y: auto;
  max-height: 600px;
  width: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.data-analysis-table {
  min-width: max-content;
  width: auto;
}

.data-analysis-table :deep(.el-table__header-wrapper) {
  background-color: #f8f9fa;
}

.data-analysis-table :deep(.el-table__header th) {
  background-color: #f8f9fa !important;
  color: #303133;
  font-weight: 600;
  border-bottom: 2px solid #e4e7ed;
}

.data-analysis-table :deep(.el-table__body tr:hover > td) {
  background-color: #f5f7fa !important;
}

.data-analysis-table :deep(.el-table__row--striped td) {
  background-color: #fafafa;
}

.data-analysis-table :deep(.el-table__row[data-parent="true"] td) {
  background-color: #e8f5e8 !important;
  font-weight: bold;
  color: #2d5a2d;
}

.data-analysis-table :deep(.el-table__row[data-child="true"] td) {
  background-color: #f9f9f9 !important;
  padding-left: 20px;
}

.data-analysis-table :deep(.el-table__body td:nth-child(1)),
.data-analysis-table :deep(.el-table__header th:nth-child(1)) {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__body td:nth-child(2)) {
  text-align: left !important;
}

.data-analysis-table :deep(.el-table__header th:nth-child(2)) {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__body td:nth-child(3)) {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header th:nth-child(3)) {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__body td:nth-child(n+4)) {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header) th {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header) .el-table__cell {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header) th[colspan] {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header) th[colspan] .el-table__cell {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header-wrapper) .el-table__header thead tr th {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header-wrapper) .el-table__header thead tr th .el-table__cell {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__body td:nth-child(n+6)) {
  text-align: right !important;
}

.data-analysis-table :deep(.el-table__body tr) td:first-child {
  vertical-align: middle;
}

.data-analysis-table :deep(.el-table__body .special-first-row td) {
  background-color: #ffffff !important;
}

.data-analysis-table :deep(.el-table__body .special-second-row td) {
  background-color: #ffffff !important;
}

.data-analysis-table :deep(.el-table__body .special-third-row td) {
  background-color: #ffffff !important;
}

.data-analysis-table :deep(.el-table__body .special-fourth-row td) {
  background-color: #ffffff !important;
}
.data-analysis-table :deep(.el-table__body .special-fifth-row td) {
  background-color: #ffffff !important;
}

.data-analysis-table :deep(.el-table__body td[rowspan]) {
  vertical-align: middle !important;
  text-align: center !important;
  font-weight: bold;
}

.data-analysis-table :deep(.el-table__body .special-first-row td:first-child) {
  text-align: center !important;
  vertical-align: middle !important;
  font-weight: bold;
  background-color: #ffffff !important;
}

.data-analysis-table :deep(.el-table__body .special-third-row td:first-child) {
  text-align: center !important;
  vertical-align: middle !important;
  font-weight: bold;
  background-color: #ffffff !important;
}

.data-analysis-table :deep(.el-table__body .special-first-row td:nth-child(2)),
.data-analysis-table :deep(.el-table__body .special-second-row td:nth-child(2)),
.data-analysis-table :deep(.el-table__body .special-third-row td:nth-child(2)),
.data-analysis-table :deep(.el-table__body .special-fourth-row td:nth-child(2)),
.data-analysis-table :deep(.el-table__body .special-fifth-row td:nth-child(2)) {
  text-align: left !important;
  padding-left: 20px;
}

.data-analysis-table :deep(.el-table__header th:nth-child(1) .el-table__cell) {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header th),
.data-analysis-table :deep(.el-table__header .el-table__cell) {
  text-align: center !important;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .kpi-overview .el-col {
    margin-bottom: 16px;
  }
  
  .charts-section .el-col {
    margin-bottom: 20px;
  }
}
</style>
