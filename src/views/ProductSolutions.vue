<template>
  <div class="product-solutions">
    <div class="header">
      <h2>产品方案</h2>
      <div class="controls">
        <el-button 
          :type="isEditable ? 'warning' : 'primary'" 
          @click="toggleEdit"
          :icon="isEditable ? Lock : Edit"
        >
          {{ isEditable ? '锁定' : '编辑' }}
        </el-button>
        <!-- <el-button type="success" @click="exportToExcel" :icon="Download">
          导出结果
        </el-button> -->
      </div>
    </div>

    <div class="content-wrapper">
      <el-card class="solution-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span class="card-title">可再生能源综合制甲醇高效利用系统</span>
          </div>
        </template>

        <div class="solution-content">
          <!-- 左侧流程图区域 -->
          <div class="flowchart-section">
            <div class="flowchart-container">
              <img 
                src="/public/images/testImage.svg" 
                alt="产品方案流程图" 
                class="flowchart-image" 
                @error="handleImageError" 
              />
            </div>
          </div>

          <!-- 右侧数据表格区域 -->
          <div class="data-section">
            <h4>配置结果</h4>
            <el-table :data="tableData" border class="result-table" :max-height="600">
              <el-table-column prop="parameter" label="参数名称" width="200">
                <template #default="{ row }">
                  <span class="parameter-name">{{ row.parameter }}</span>
                </template>
              </el-table-column>

              <el-table-column prop="value" label="数值" width="150">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.value"
                    :precision="row.precision || 0"
                    :step="row.step || 1"
                    :min="row.min || 0"
                    :max="row.max"
                    :disabled="!isEditable"
                    size="small"
                    style="width: 100%"
                    controls-position="right"
                  />
                </template>
              </el-table-column>

              <el-table-column prop="unit" label="单位" width="100">
                <template #default="{ row }">
                  <span>{{ row.unit }}</span>
                </template>
              </el-table-column>

              <el-table-column
                prop="category"
                label="类别"
                width="120"
                :filters="categoryFilters"
                :filter-method="filterByCategory"
              >
                <template #default="{ row }">
                  <el-tag
                    :type="getCategoryType(row.category)"
                    :color="getCategoryColor(row.category)"
                    size="small"
                  >
                    {{ row.category }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column label="备注" min-width="200">
                <template #default="{ row }">
                  <span class="remark">{{ row.remark }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Lock, Download } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import { useProjectStore } from '@/store/modules/project'

// 控制编辑状态
const isEditable = ref(false)
const projectStore = useProjectStore()

// 类别筛选选项
const categoryFilters = ref([
  { text: '容量优化配置结果', value: '容量优化配置结果' },
  { text: '总体产品方案', value: '总体产品方案' },
  { text: '系统经济性连算结果', value: '系统经济性连算结果' },
  { text: '经济性评价', value: '经济性评价' },
  { text: '成本构成', value: '成本构成' },
  { text: '收入结构', value: '收入结构' }
])

// 类别筛选方法
const filterByCategory = (value, row, column) => {
  return row.category === value
}


const tableData = ref([
  // 容量优化配置结果
  {
    parameter: '合成氨装置规模',
    value: 100,
    unit: '万吨/年',
    category: '容量优化配置结果',
    remark: '合成氨生产装置年产能规模',
    precision: 0,
    step: 1,
    min: 0
  },
  {
    parameter: '氨合成系统规模',
    value: 25,
    unit: 'MW',
    category: '容量优化配置结果',
    remark: '氨合成系统装机容量',
    precision: 0,
    step: 1,
    min: 0
  },
  {
    parameter: '电化学储能规模',
    value: 25,
    unit: 'MW',
    category: '容量优化配置结果',
    remark: '电化学储能系统装机规模',
    precision: 0,
    step: 1,
    min: 0
  },
  {
    parameter: '储氢规模',
    value: 700,
    unit: 't',
    category: '容量优化配置结果',
    remark: '氢气储存容量规模',
    precision: 0,
    step: 1,
    min: 0
  },

  // 总体产品方案
  {
    parameter: '年合成氨产量',
    value: 0,
    unit: '万吨/年',
    category: '总体产品方案',
    remark: '年度合成氨实际产量',
    precision: 0,
    step: 1,
    min: 0
  },
  {
    parameter: '年发电总电量',
    value: 0,
    unit: '万kWh',
    category: '总体产品方案',
    remark: '年度发电总量',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '上网电量',
    value: 0,
    unit: '万kWh',
    category: '总体产品方案',
    remark: '输送到电网的电量',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '储氢电量',
    value: 0,
    unit: '万kWh',
    category: '总体产品方案',
    remark: '用于储氢的电量',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '年制氢总量',
    value: 0,
    unit: '万Nm3',
    category: '总体产品方案',
    remark: '年度制氢总产量',
    precision: 2,
    step: 0.1,
    min: 0
  },

  // 系统经济性连算结果
  {
    parameter: '项目周期',
    value: 0,
    unit: '年',
    category: '系统经济性连算结果',
    remark: '项目运营周期',
    precision: 0,
    step: 1,
    min: 1
  },
  {
    parameter: '全生命周期总利润',
    value: 0,
    unit: '万元',
    category: '系统经济性连算结果',
    remark: '项目全生命周期总利润',
    precision: 0,
    step: 100,
    min: 0
  },

  // 经济性评价
  {
    parameter: '项目周期',
    value: 0,
    unit: '年',
    category: '经济性评价',
    remark: '经济性评价项目周期',
    precision: 0,
    step: 1,
    min: 1
  },
  {
    parameter: '氨气内部收益率',
    value: 0,
    unit: '%',
    category: '经济性评价',
    remark: '氨气项目内部收益率',
    precision: 2,
    step: 0.01,
    min: 0
  },
  {
    parameter: '氨气净现值',
    value: 0,
    unit: '%',
    category: '经济性评价',
    remark: '氨气项目净现值',
    precision: 2,
    step: 0.01,
    min: 0
  },
  {
    parameter: '生命周期利润',
    value: 0,
    unit: '万元',
    category: '经济性评价',
    remark: '项目生命周期总利润',
    precision: 0,
    step: 100,
    min: 0
  },

  // 成本构成
  {
    parameter: '总计',
    value: 0,
    unit: '万元',
    category: '成本构成',
    remark: '总成本',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '光伏发电',
    value: 0,
    unit: '万元',
    category: '成本构成',
    remark: '光伏发电系统成本',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '风电',
    value: 0,
    unit: '万元',
    category: '成本构成',
    remark: '风力发电系统成本',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '光伏送出线路',
    value: 0,
    unit: '万元',
    category: '成本构成',
    remark: '光伏电力输送线路成本',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '风电送出线路',
    value: 0,
    unit: '万元',
    category: '成本构成',
    remark: '风电输送线路成本',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '升压站',
    value: 0,
    unit: '万元',
    category: '成本构成',
    remark: '升压站建设成本',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '降压站',
    value: 0,
    unit: '万元',
    category: '成本构成',
    remark: '降压站建设成本',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '电池储能系统',
    value: 0,
    unit: '万元',
    category: '成本构成',
    remark: '电池储能系统成本',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '储氢系统',
    value: 0,
    unit: '万元',
    category: '成本构成',
    remark: '储氢系统建设成本',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '制氢系统',
    value: 0,
    unit: '万元',
    category: '成本构成',
    remark: '制氢系统建设成本',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '制氨公用设施',
    value: 0,
    unit: '万元',
    category: '成本构成',
    remark: '制氨公用设施成本',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '其他1',
    value: 0,
    unit: '万元',
    category: '成本构成',
    remark: '其他成本项目1',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '其他2',
    value: 0,
    unit: '万元',
    category: '成本构成',
    remark: '其他成本项目2',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '其他3',
    value: 0,
    unit: '万元',
    category: '成本构成',
    remark: '其他成本项目3',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '其他4',
    value: 0,
    unit: '万元',
    category: '成本构成',
    remark: '其他成本项目4',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '其他5',
    value: 0,
    unit: '万元',
    category: '成本构成',
    remark: '其他成本项目5',
    precision: 0,
    step: 100,
    min: 0
  },

  // 收入结构
  {
    parameter: '售氧',
    value: 0,
    unit: '万元',
    category: '收入结构',
    remark: '售氧收入',
    precision: 0,
    step: 100,
    min: 0
  },{
    parameter: '售电',
    value: 0,
    unit: '万元',
    category: '收入结构',
    remark: '售电收入',
    precision: 0,
    step: 100,
    min: 0
  },
  {
    parameter: '售氨',
    value: 0,
    unit: '万元',
    category: '收入结构',
    remark: '售氨收入',
    precision: 0,
    step: 100,
    min: 0
  }
])

// 获取类别标签类型
const getCategoryType = (category) => {
  const typeMap = {
    '容量优化配置结果': 'primary',
    '总体产品方案': 'success',
    '系统经济性连算结果': 'warning',
    '经济性评价': 'info',
    '成本构成': 'danger',
  }
  return typeMap[category] || 'primary'
}

// 获取类别标签自定义颜色
const getCategoryColor = (category) => {
  if (category === '收入结构') {
    return '#e4e7b6' // 紫色
  }
  return undefined
}

// 处理图片加载错误
const handleImageError = (e) => {
  e.target.src = '/placeholder-flowchart.png' // 可以设置一个默认的占位图
}

// 切换编辑状态
const toggleEdit = () => {
  isEditable.value = !isEditable.value
  ElMessage({
    type: 'success',
    message: isEditable.value ? '已开启编辑模式' : '已锁定数据'
  })
}

// 导出Excel功能
const exportToExcel = () => {
  try {
    // 创建工作簿
    const wb = XLSX.utils.book_new()
    
    // 准备数据
    const data = [
      ['产品方案配置结果', '', '', '', ''],
      ['参数名称', '数值', '单位', '类别', '备注'],
      ...tableData.value.map(item => [
        item.parameter,
        item.value,
        item.unit,
        item.category,
        item.remark
      ])
    ]
    
    // 创建工作表
    const ws = XLSX.utils.aoa_to_sheet(data)
    
    // 设置列宽
    ws['!cols'] = [
      { wch: 20 },
      { wch: 15 },
      { wch: 12 },
      { wch: 15 },
      { wch: 25 }
    ]
    
    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '产品方案配置结果')
    
    // 生成文件名
    const fileName = `产品方案配置结果_${new Date().toLocaleDateString().replace(/\//g, '-')}.xlsx`
    
    // 导出文件
    XLSX.writeFile(wb, fileName)
    
    ElMessage({
      type: 'success',
      message: '导出成功！'
    })
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage({
      type: 'error',
      message: '导出失败，请重试'
    })
  }
}

// 监听数据变化，实时更新到Pinia
watch(tableData, (newData) => {
  projectStore.updateCurrentInputData('solution', newData);
  console.log('产品方案数据已更新到Pinia:', newData);
}, { deep: true });
</script>

<style scoped>
.product-solutions {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 4px;
}

.header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 600;
}

.controls {
  display: flex;
  gap: 12px;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.solution-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.solution-content {
  display: grid;
  grid-template-columns: 2fr 3fr;
  gap: 20px;
  min-height: 500px;
  padding: 20px;
}

@media (max-width: 1200px) {
  .solution-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

.flowchart-section {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 10px;
  background-color: #fafafa;
}

.flowchart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.flowchart-image {
  max-width: 100%;
  max-height: 400px;
  object-fit: contain;
  border-radius: 4px;
}

.data-section h4 {
  margin-bottom: 16px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.result-table {
  width: 100%;
}

.parameter-name {
  font-weight: 500;
}

.remark {
  color: #606266;
  font-size: 12px;
}

.el-card :deep(.el-card__header) {
  background-color: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 20px;
}

.el-card :deep(.el-card__body) {
  padding: 0;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  font-weight: 600;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .controls {
    width: 100%;
    justify-content: flex-end;
  }
  
  .solution-content {
    grid-template-columns: 1fr;
    padding: 10px;
  }
}
</style>
