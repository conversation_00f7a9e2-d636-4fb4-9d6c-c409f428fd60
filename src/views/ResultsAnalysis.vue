<template>
  <div class="results-page">
    <div class="page-header">
      <h1>经济测算结果分析</h1>
      <div class="header-actions">
      </div>
    </div>

    <div class="results-content">

      <!-- 关键指标总览 -->
      <el-row :gutter="20" class="kpi-overview">
        <el-col :span="6">
          <el-card class="kpi-card">
            <div class="kpi-content">
              <div class="kpi-value">{{ formatNumber(totalInvestment) }}</div>
              <div class="kpi-label">总投资 (万元)</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="kpi-card">
            <div class="kpi-content">
              <div class="kpi-value">{{ formatNumber(totalCapacity) }}</div>
              <div class="kpi-label">总装机容量 (MW)</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="kpi-card">
            <div class="kpi-content">
              <div class="kpi-value">{{ formatPercent(averageIRR) }}</div>
              <div class="kpi-label">平均内部收益率</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="kpi-card">
            <div class="kpi-content">
              <div class="kpi-value">{{ formatNumber(totalNPV) }}</div>
              <div class="kpi-label">总净现值 (万元)</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 图表展示区域 -->
      <el-row :gutter="20" class="charts-section">
        <!-- 投资构成饼图 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <h3>投资构成分析</h3>
            </template>
            <v-chart
              class="chart"
              :option="investmentCompositionOption"
              :autoresize="true"
            />
          </el-card>
        </el-col>

        <!-- 收益率对比柱图 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <h3>各项目收益率对比</h3>
            </template>
            <v-chart
              class="chart"
              :option="irrComparisonOption"
              :autoresize="true"
            />
          </el-card>
        </el-col>
      </el-row>

      

      <el-row :gutter="20" class="charts-section">
        <!-- 现金流分析线图 -->
        <el-col :span="24">
          <el-card>
            <template #header>
              <div class="chart-header">
                <h3>现金流分析</h3>
                <el-radio-group v-model="cashflowType" @change="updateCashflowChart">
                  <el-radio label="cumulative" value="cumulative">累计现金流</el-radio>
                  <el-radio label="annual" value="annual">年度现金流</el-radio>
                </el-radio-group>
              </div>
            </template>
            <v-chart
              class="chart-large"
              :option="cashflowOption"
              :autoresize="true"
            />
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="charts-section">
        <!-- 敏感性分析 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <h3>敏感性分析</h3>
            </template>
            <v-chart
              class="chart"
              :option="sensitivityOption"
              :autoresize="true"
            />
          </el-card>
        </el-col>

        <!-- 风险分析雷达图 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <h3>风险评估</h3>
            </template>
            <v-chart
              class="chart"
              :option="riskAssessmentOption"
              :autoresize="true"
            />
          </el-card>
        </el-col>
      </el-row>

      
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'



interface DetailedResult {
  project: string
  investment: number
  capacity: number
  annualRevenue: number
  irr: number
  npv: number
  paybackPeriod: number
  riskLevel: string
}
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import {
  PieChart,
  BarChart,
  LineChart,
  RadarChart
} from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  RadarComponent
} from 'echarts/components'
import VChart from 'vue-echarts'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  PieChart,
  BarChart,
  LineChart,
  RadarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  RadarComponent
])

const route = useRoute()
const router = useRouter()

// 数据状态
const inputData = ref<any>(null)
const cashflowType = ref<'cumulative' | 'annual'>('cumulative')



// KPI 数据
const totalInvestment = ref<number>(120000)
const totalCapacity = ref<number>(150)
const averageIRR = ref<number>(12.5)
const totalNPV = ref<number>(45000)

// 详细结果数据
const detailedResults = ref<DetailedResult[]>([
  {
    project: '太阳能发电',
    investment: 50000,
    capacity: 100,
    annualRevenue: 6300,
    irr: 13.2,
    npv: 25000,
    paybackPeriod: 8.5,
    riskLevel: '低'
  },
  {
    project: '风能发电',
    investment: 40000,
    capacity: 50,
    annualRevenue: 3800,
    irr: 11.8,
    npv: 18000,
    paybackPeriod: 9.2,
    riskLevel: '中'
  },
  {
    project: '储能系统',
    investment: 30000,
    capacity: 0,
    annualRevenue: 2400,
    irr: 12.5,
    npv: 2000,
    paybackPeriod: 12.5,
    riskLevel: '高'
  }
])


// 图表配置
const investmentCompositionOption = reactive({
  title: {
    text: '投资构成',
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b} : {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '投资构成',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 50000, name: '太阳能发电' },
        { value: 40000, name: '风能发电' },
        { value: 30000, name: '储能系统' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
})

const irrComparisonOption = reactive({
  title: {
    text: '各项目收益率对比',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: ['太阳能发电', '风能发电', '储能系统']
  },
  yAxis: {
    type: 'value',
    name: '内部收益率(%)'
  },
  series: [
    {
      name: '内部收益率',
      type: 'bar',
      data: [13.2, 11.8, 12.5],
      itemStyle: {
        color: function(params) {
          const colors = ['#5470c6', '#91cc75', '#fac858']
          return colors[params.dataIndex]
        }
      }
    }
  ]
})

const cashflowOption = reactive({
  title: {
    text: '项目现金流分析',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['太阳能发电', '风能发电', '储能系统'],
    top: 30
  },
  xAxis: {
    type: 'category',
    data: Array.from({length: 25}, (_, i) => `第${i+1}年`)
  },
  yAxis: {
    type: 'value',
    name: '现金流(万元)'
  },
  series: [
    {
      name: '太阳能发电',
      type: 'line',
      data: generateCashflowData(25, 6300, 50000),
      smooth: true
    },
    {
      name: '风能发电',
      type: 'line',
      data: generateCashflowData(20, 3800, 40000),
      smooth: true
    },
    {
      name: '储能系统',
      type: 'line',
      data: generateCashflowData(15, 2400, 30000),
      smooth: true
    }
  ]
})

const sensitivityOption = reactive({
  title: {
    text: '敏感性分析',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['电价变化', '投资成本变化', '发电量变化'],
    top: 30
  },
  xAxis: {
    type: 'category',
    data: ['-20%', '-10%', '0%', '+10%', '+20%']
  },
  yAxis: {
    type: 'value',
    name: 'NPV变化率(%)'
  },
  series: [
    {
      name: '电价变化',
      type: 'line',
      data: [-25, -12, 0, 15, 32],
      lineStyle: { width: 3 }
    },
    {
      name: '投资成本变化',
      type: 'line',
      data: [20, 10, 0, -8, -18],
      lineStyle: { width: 3 }
    },
    {
      name: '发电量变化',
      type: 'line',
      data: [-18, -9, 0, 12, 26],
      lineStyle: { width: 3 }
    }
  ]
})

const riskAssessmentOption = reactive({
  title: {
    text: '风险评估雷达图',
    left: 'center'
  },
  tooltip: {},
  legend: {
    data: ['太阳能发电', '风能发电', '储能系统'],
    top: 30
  },
  radar: {
    indicator: [
      { name: '技术风险', max: 5 },
      { name: '市场风险', max: 5 },
      { name: '政策风险', max: 5 },
      { name: '财务风险', max: 5 },
      { name: '环境风险', max: 5 },
      { name: '运营风险', max: 5 }
    ]
  },
  series: [
    {
      name: '风险评估',
      type: 'radar',
      data: [
        {
          value: [2, 2, 3, 2, 1, 2],
          name: '太阳能发电'
        },
        {
          value: [3, 3, 3, 3, 2, 3],
          name: '风能发电'
        },
        {
          value: [4, 4, 2, 4, 2, 4],
          name: '储能系统'
        }
      ]
    }
  ]
})

// 生成现金流数据
function generateCashflowData(years: number, annualRevenue: number, initialInvestment: number): number[] {
  const data: number[] = []
  let cumulative = -initialInvestment
  
  for (let i = 0; i < years; i++) {
    if (cashflowType.value === 'cumulative') {
      cumulative += annualRevenue
      data.push(cumulative)
    } else {
      data.push(i === 0 ? -initialInvestment : annualRevenue)
    }
  }
  
  return data
}

// 更新现金流图表
const updateCashflowChart = () => {
  cashflowOption.series = [
    {
      name: '太阳能发电',
      type: 'line',
      data: generateCashflowData(25, 6300, 50000),
      smooth: true
    },
    {
      name: '风能发电',
      type: 'line',
      data: generateCashflowData(20, 3800, 40000),
      smooth: true
    },
    {
      name: '储能系统',
      type: 'line',
      data: generateCashflowData(15, 2400, 30000),
      smooth: true
    }
  ]
}

// 工具函数
const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('zh-CN').format(num)
}

const formatPercent = (num: number): string => {
  return `${num.toFixed(1)}%`
}

const getRiskTagType = (riskLevel: string): string => {
  const typeMap: Record<string, string> = {
    '低': 'success',
    '中': 'warning',
    '高': 'danger'
  }
  return typeMap[riskLevel] || 'info'
}

// 页面操作
const goBack = (): void => {
  router.push('/calculation/energy')
}

const exportReport = (): void => {
  // 获取当前项目ID
  const projectId = route.query.projectId || route.params.projectId || 1
  
  // 打开Excel预览页面
  const previewUrl = `/export-preview?projectId=${projectId}`
  window.open(previewUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes')
}


</script>

<style scoped>
.results-page {
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.page-header h1 {
  color: #303133;
  margin: 0;
  font-size: 28px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.kpi-overview {
  margin-bottom: 30px;
}

.kpi-card {
  text-align: center;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
}

.kpi-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.kpi-content {
  padding: 20px 0;
}

.kpi-value {
  font-size: 28px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 8px;
}

.kpi-label {
  font-size: 14px;
  color: #606266;
}

.charts-section {
  margin-bottom: 30px;
}

.chart {
  height: 350px;
  width: 100%;
}

.chart-large {
  height: 400px;
  width: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h3 {
  margin: 0;
  color: #303133;
}

.data-info-section {
  margin-bottom: 20px;
}

.api-test-section {
  margin-bottom: 30px;
}

.api-test-section .el-card {
  border: 1px solid #e4e7ed;
}

.api-test-section .el-button {
  margin-left: 8px;
}

.data-table-section {
  margin-top: 30px;
}

:deep(.el-card__header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-card__header h3) {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

/* 现金流量表样式 */
.data-analysis-table-container {
  overflow-x: auto;
  overflow-y: auto;
  max-height: 600px;
  width: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.data-analysis-table {
  min-width: max-content;
  width: auto;
}

.data-analysis-table :deep(.el-table__header-wrapper) {
  background-color: #f8f9fa;
}

.data-analysis-table :deep(.el-table__header th) {
  background-color: #f8f9fa !important;
  color: #303133;
  font-weight: 600;
  border-bottom: 2px solid #e4e7ed;
}

.data-analysis-table :deep(.el-table__body tr:hover > td) {
  background-color: #f5f7fa !important;
}

.data-analysis-table :deep(.el-table__row--striped td) {
  background-color: #fafafa;
}

.data-analysis-table :deep(.el-table__row[data-parent="true"] td) {
  background-color: #e8f5e8 !important;
  font-weight: bold;
  color: #2d5a2d;
}

.data-analysis-table :deep(.el-table__row[data-child="true"] td) {
  background-color: #f9f9f9 !important;
  padding-left: 20px;
}

.data-analysis-table :deep(.el-table__body td:nth-child(1)),
.data-analysis-table :deep(.el-table__header th:nth-child(1)) {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__body td:nth-child(2)) {
  text-align: left !important;
}

.data-analysis-table :deep(.el-table__header th:nth-child(2)) {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__body td:nth-child(3)) {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header th:nth-child(3)) {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__body td:nth-child(n+4)) {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header) th {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header) .el-table__cell {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header) th[colspan] {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header) th[colspan] .el-table__cell {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header-wrapper) .el-table__header thead tr th {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header-wrapper) .el-table__header thead tr th .el-table__cell {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__body td:nth-child(n+6)) {
  text-align: right !important;
}

.data-analysis-table :deep(.el-table__body tr) td:first-child {
  vertical-align: middle;
}

.data-analysis-table :deep(.el-table__body .special-first-row td) {
  background-color: #ffffff !important;
}

.data-analysis-table :deep(.el-table__body .special-second-row td) {
  background-color: #ffffff !important;
}

.data-analysis-table :deep(.el-table__body .special-third-row td) {
  background-color: #ffffff !important;
}

.data-analysis-table :deep(.el-table__body .special-fourth-row td) {
  background-color: #ffffff !important;
}
.data-analysis-table :deep(.el-table__body .special-fifth-row td) {
  background-color: #ffffff !important;
}

.data-analysis-table :deep(.el-table__body td[rowspan]) {
  vertical-align: middle !important;
  text-align: center !important;
  font-weight: bold;
}

.data-analysis-table :deep(.el-table__body .special-first-row td:first-child) {
  text-align: center !important;
  vertical-align: middle !important;
  font-weight: bold;
  background-color: #ffffff !important;
}

.data-analysis-table :deep(.el-table__body .special-third-row td:first-child) {
  text-align: center !important;
  vertical-align: middle !important;
  font-weight: bold;
  background-color: #ffffff !important;
}

.data-analysis-table :deep(.el-table__body .special-first-row td:nth-child(2)),
.data-analysis-table :deep(.el-table__body .special-second-row td:nth-child(2)),
.data-analysis-table :deep(.el-table__body .special-third-row td:nth-child(2)),
.data-analysis-table :deep(.el-table__body .special-fourth-row td:nth-child(2)),
.data-analysis-table :deep(.el-table__body .special-fifth-row td:nth-child(2)) {
  text-align: left !important;
  padding-left: 20px;
}

.data-analysis-table :deep(.el-table__header th:nth-child(1) .el-table__cell) {
  text-align: center !important;
}

.data-analysis-table :deep(.el-table__header th),
.data-analysis-table :deep(.el-table__header .el-table__cell) {
  text-align: center !important;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .kpi-overview .el-col {
    margin-bottom: 16px;
  }
  
  .charts-section .el-col {
    margin-bottom: 20px;
  }
}
</style>
