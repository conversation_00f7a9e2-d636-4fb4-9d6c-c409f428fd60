<template>
  <div class="dashboard-page">
    <div class="welcome-section">
      <h1>欢迎使用可再生能源制氢合成氨经济性容量配置软件</h1>
      <p>专业的能源项目投资回报分析与经济效益评估系统</p>
    </div>

    <div class="feature-cards">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="feature-card" @click="navigateTo('/calculation/energy')">
            <div class="card-content">
              <el-icon class="feature-icon" size="48"><Lightning /></el-icon>
              <h3>能源项目测算</h3>
              <p>太阳能、风能、储能等新能源项目的经济性分析</p>
              <div class="card-stats">
                <span>支持 3+ 种能源类型</span>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card class="feature-card" @click="navigateTo('/calculation/investment')">
            <div class="card-content">
              <el-icon class="feature-icon" size="48"><Money /></el-icon>
              <h3>投资项目测算</h3>
              <p>通用投资项目的财务分析与风险评估</p>
              <div class="card-stats">
                <span>NPV、IRR等指标</span>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card class="feature-card" @click="navigateTo('/results')">
            <div class="card-content">
              <el-icon class="feature-icon" size="48"><PieChart /></el-icon>
              <h3>结果分析</h3>
              <p>可视化图表展示分析结果，支持多维度对比</p>
              <div class="card-stats">
                <span>8+ 种图表类型</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ projectCount }}</div>
            <div class="stat-label">已完成项目</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ totalInvestment }}</div>
            <div class="stat-label">累计投资额(亿元)</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ avgReturn }}</div>
            <div class="stat-label">平均收益率</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ riskProjects }}</div>
            <div class="stat-label">风险项目</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <div class="recent-activity">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>最近活动</span>
            <el-button text @click="viewAllActivity">查看全部</el-button>
          </div>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="activity.timestamp"
            :type="activity.type"
          >
            <div class="activity-content">
              <h4>{{ activity.title }}</h4>
              <p>{{ activity.description }}</p>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Lightning, Money, PieChart } from '@element-plus/icons-vue'

const router = useRouter()

// 统计数据
const projectCount = ref(0)
const totalInvestment = ref(0)
const avgReturn = ref('0%')
const riskProjects = ref(0)

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '光伏发电项目测算完成',
    description: '100MW光伏项目，IRR 13.2%，NPV 2.5亿元',
    timestamp: '2025-07-01 14:30',
    type: 'success'
  },
  {
    id: 2,
    title: '风电项目风险评估',
    description: '50MW风电项目存在政策风险，建议谨慎投资',
    timestamp: '2025-07-01 10:15',
    type: 'warning'
  },
  {
    id: 3,
    title: '储能系统分析报告',
    description: '200MWh储能项目经济性分析报告已生成',
    timestamp: '2025-06-30 16:45',
    type: 'primary'
  }
])

// 页面导航
const navigateTo = (path) => {
  router.push(path)
}

// 查看全部活动
const viewAllActivity = () => {
  console.log('查看全部活动')
}

// 数字动画效果
const animateNumber = (target, duration = 2000) => {
  const start = 0
  const increment = target / (duration / 16)
  let current = start
  
  const timer = setInterval(() => {
    current += increment
    if (current >= target) {
      current = target
      clearInterval(timer)
    }
    return Math.floor(current)
  }, 16)
  
  return timer
}

// 页面初始化
onMounted(() => {
  // 模拟加载统计数据
  setTimeout(() => {
    projectCount.value = 126
    totalInvestment.value = 45.8
    avgReturn.value = '12.3%'
    riskProjects.value = 8
  }, 500)
})
</script>

<style scoped>
.dashboard-page {
  margin: 0 auto;
}

.welcome-section {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.welcome-section h1 {
  font-size: 32px;
  margin-bottom: 16px;
  font-weight: 600;
}

.welcome-section p {
  font-size: 18px;
  opacity: 0.9;
  margin: 0;
}

.feature-cards {
  margin-bottom: 40px;
}

.feature-card {
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
}

.card-content {
  text-align: center;
  padding: 20px;
}

.feature-icon {
  color: #409EFF;
  margin-bottom: 16px;
}

.card-content h3 {
  margin: 0 0 12px 0;
  font-size: 20px;
  color: #303133;
}

.card-content p {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.card-stats {
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.card-stats span {
  color: #909399;
  font-size: 12px;
}

.stats-section {
  margin-bottom: 40px;
}

.stat-item {
  text-align: center;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 32px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.recent-activity {
  margin-bottom: 40px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-content h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #303133;
}

.activity-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

@media (max-width: 768px) {
  .welcome-section {
    padding: 30px 16px;
  }
  
  .welcome-section h1 {
    font-size: 24px;
  }
  
  .welcome-section p {
    font-size: 16px;
  }
  
  .feature-cards .el-col {
    margin-bottom: 20px;
  }
  
  .stats-section .el-col {
    margin-bottom: 16px;
  }
}
</style>
