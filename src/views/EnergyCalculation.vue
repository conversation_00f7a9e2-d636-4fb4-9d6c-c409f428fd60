<template>
  <div class="energy-calculation-page">
    <div class="page-header">
      <div class="header-content">
        <div class="header-text">
          <h1>能源项目经济测算</h1>
          <p>请输入相关参数进行经济性分析计算</p>
        </div>
        <div class="header-actions">
          <el-upload
              ref="uploadRef"
              :auto-upload="false"
              :show-file-list="false"
              accept=".xlsx,.xls"
              :on-change="handleFileChange"
              :before-upload="beforeUpload"
          >
            <el-tooltip content="上传前请先下载模板填写" placement="top">
              <el-button type="success" :icon="Upload"> 导入Excel</el-button>
            </el-tooltip>
          </el-upload>
          <el-button type="info" @click="downloadTemplate" :icon="Download"> 下载模板</el-button>
        </div>
      </div>
    </div>

    <!-- 光伏参数模块 -->
    <CalculationModule
        title="光伏参数"
        :flowchart-image="photovoltaicFlowchart"
        :initial-data="photovoltaicData"
        :showCalculateBtn="false"
        @data-change="handleDataChange"
    />

    <!-- 风电参数模块 -->
    <CalculationModule title="风电参数" :flowchart-image="windPowerFlowchart" :initial-data="windPowerData"
                       @data-change="handleDataChange"/>

    <!-- 电网系统模块 -->
    <CalculationModule title="电网系统" :flowchart-image="gridSystemFlowchart" :initial-data="gridSystemData"
                       @data-change="handleDataChange"/>

    <!-- 制氢厂参数模块 -->
    <CalculationModule title="制氢厂参数" :flowchart-image="hydrogenPlantFlowchart" :initial-data="hydrogenPlantData"
                       @data-change="handleDataChange"/>

    <!-- 融资贷款模块 -->
    <CalculationModule title="融资贷款" :flowchart-image="financingFlowchart" :initial-data="financingData"
                       @data-change="handleDataChange"/>

    <!-- 液氨参数模块 -->
    <CalculationModule title="液氨参数" :flowchart-image="liquidAmmoniaFlowchart" :initial-data="liquidAmmoniaData"
                       @data-change="handleDataChange"/>

    <!-- 储能电站模块 -->
    <CalculationModule title="储能电站" :flowchart-image="energyStoragePowerStationFlowchart" :initial-data="energyStoragePowerStationData"
                       @data-change="handleDataChange"/>
    <!-- 计算按钮区域 -->
    <div class="calculation-actions">
      <el-card>
        <div class="actions-content">
          <div class="summary-info">
            <h3>计算摘要</h3>
            <p>已配置 {{ configuredModules }} 个计算模块</p>
          </div>
          <div class="action-buttons">
            <el-button size="large" @click="handleReset">重置数据</el-button>
            <el-button type="primary" size="large" @click="handleCalculateAll" :loading="calculating">
              {{ calculating ? '计算中...' : '开始计算' }}
            </el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, computed} from 'vue';
import {useRouter} from 'vue-router';
import {ElMessage, ElMessageBox} from 'element-plus';
import {Upload, Download} from '@element-plus/icons-vue';
import * as XLSX from 'xlsx';
import {transformArrayToObject} from '@/utils/convert';
import CalculationModule from '@/components/CalculationModule.vue';
import {calculate} from '@/api/modules/calculate'
import { useProjectStore } from '@/store/modules/project'

const router = useRouter();
const calculating = ref(false);
const uploadRef = ref(null);
const projectStore = useProjectStore();

// 流程图图片路径
const photovoltaicFlowchart = ref('/images/testImage.svg');
const windPowerFlowchart = ref('/images/testImage.svg');
const gridSystemFlowchart = ref('/images/testImage.svg');
const hydrogenPlantFlowchart = ref('/images/testImage.svg');
const financingFlowchart = ref('/images/testImage.svg');
const liquidAmmoniaFlowchart = ref('/images/testImage.svg');
const energyStoragePowerStationFlowchart = ref('/images/testImage.svg');
// 光伏参数模块数据
const photovoltaicData = ref([
  {
    parameter: '光伏装机容量（MW）',
    value: 500.00,
    unit: 'MW',
    category: '技术参数',
    remark: '光伏发电装机容量',
    description: '光伏发电系统的总装机容量',
    min: 0,
    step: 1,
    precision: 2,
  },
  {
    parameter: '光伏利用小时数（h)',
    value: 1650.00,
    unit: 'h',
    category: '技术参数',
    remark: '年均光伏利用小时数',
    description: '光伏系统年均有效发电小时数',
    min: 0,
    step: 10,
    precision: 2,
  },
  {
    parameter: '首年发电量衰减率（%）',
    value: 2.00,
    unit: '%',
    category: '技术参数',
    remark: '首年发电量衰减率',
    description: '光伏系统首年发电量相对衰减率',
    min: 0,
    max: 10,
    step: 0.1,
    precision: 2,
  },
  {
    parameter: '年均发电量衰减率（%）',
    value: 0.55,
    unit: '%',
    category: '技术参数',
    remark: '年均发电量衰减率',
    description: '光伏系统年均发电量衰减率',
    min: 0,
    max: 5,
    step: 0.01,
    precision: 2,
  },
  {
    parameter: '系统效率（%）',
    value: 82.3,
    unit: '%',
    category: '技术参数',
    remark: '光伏系统效率',
    description: '光伏发电系统综合效率',
    min: 50,
    max: 100,
    step: 0.1,
    precision: 1,
  },
  {
    parameter: '光伏送出线路距离（公里）',
    value: 60.00,
    unit: '公里',
    category: '技术参数',
    remark: '光伏送出线路距离',
    description: '光伏电站到电网的送出线路距离',
    min: 0,
    step: 1,
    precision: 2,
  },
  {
    parameter: '设备折旧年限（年）',
    value: 25.00,
    unit: '年',
    category: '经济参数',
    remark: '设备折旧年限',
    description: '光伏设备的折旧年限',
    min: 10,
    max: 50,
    step: 1,
    precision: 2,
  },
  {
    parameter: '上网电价(含税)（元/kwh)',
    value: 0.2829,
    unit: '元/kWh',
    category: '经济参数',
    remark: '上网电价含税',
    description: '光伏发电上网电价（含税）',
    min: 0,
    max: 2,
    step: 0.0001,
    precision: 4,
  },
]);

// 风电参数模块数据
const windPowerData = ref([
  {
    parameter: '风电装机容量（MW）',
    value: 200.00,
    unit: 'MW',
    category: '技术参数',
    remark: '风电装机容量',
    description: '风力发电系统的总装机容量',
    min: 0,
    step: 1,
    precision: 2,
  },
  {
    parameter: '风电利用小时数（h)',
    value: 3000.00,
    unit: 'h',
    category: '技术参数',
    remark: '年均风电利用小时数',
    description: '风电系统年均有效发电小时数',
    min: 0,
    step: 10,
    precision: 2,
  },
  {
    parameter: '系统效率（%）',
    value: 82.3,
    unit: '%',
    category: '技术参数',
    remark: '风电系统效率',
    description: '风力发电系统综合效率',
    min: 50,
    max: 100,
    step: 0.1,
    precision: 1,
  },
  {
    parameter: '风电送出线路距离（公里）',
    value: 60.00,
    unit: '公里',
    category: '技术参数',
    remark: '风电送出线路距离',
    description: '风电场到电网的送出线路距离',
    min: 0,
    step: 1,
    precision: 2,
  },
  {
    parameter: '设备折旧年限（年）',
    value: 25.00,
    unit: '年',
    category: '经济参数',
    remark: '设备折旧年限',
    description: '风电设备的折旧年限',
    min: 10,
    max: 50,
    step: 1,
    precision: 2,
  },
  {
    parameter: '上网电价(含税)（元/kwh)',
    value: 0.2829,
    unit: '元/kWh',
    category: '经济参数',
    remark: '上网电价含税',
    description: '风电上网电价（含税）',
    min: 0,
    max: 2,
    step: 0.0001,
    precision: 4,
  },
]);

// 电网系统模块数据
const gridSystemData = ref([
  {
    parameter: '送出线路长度',
    value: 0,
    unit: '公里',
    category: '技术参数',
    remark: '送出线路长度',
    description: '电网送出线路的总长度',
    min: 0,
    step: 1,
    precision: 2,
  },
  {
    parameter: '送出线路单位造价',
    value: 0.00,
    unit: '万元/公里',
    category: '经济参数',
    remark: '送出线路单位造价',
    description: '送出线路每公里的建设造价',
    min: 0,
    step: 1,
    precision: 2,
  },
  {
    parameter: '升压站规模',
    value: 700,
    unit: 'MVA',
    category: '技术参数',
    remark: '升压站规模',
    description: '升压站的变压器容量规模',
    min: 0,
    step: 1,
    precision: 2,
  },
  {
    parameter: '升压站单位造价',
    value: 1650.00,
    unit: '万元/MVA',
    category: '经济参数',
    remark: '升压站单位造价',
    description: '升压站每MVA的建设造价',
    min: 0,
    step: 1,
    precision: 2,
  },
  {
    parameter: '降压站规模',
    value: 100,
    unit: 'MVA',
    category: '技术参数',
    remark: '降压站规模',
    description: '降压站的变压器容量规模',
    min: 0,
    step: 1,
    precision: 2,
  },
  {
    parameter: '降压站单位造价',
    value: 0,
    unit: '万元/MVA',
    category: '经济参数',
    remark: '降压站单位造价',
    description: '降压站每MVA的建设造价',
    min: 0,
    step: 1,
    precision: 2,
  },
]);

// 制氢厂参数模块数据
const hydrogenPlantData = ref([
  {
    parameter: '制氢容量（MW)',
    value: 100.00,
    unit: 'MW',
    category: '技术参数',
    remark: '制氢设备容量',
    description: '制氢厂的制氢设备容量',
    min: 0,
    step: 1,
    precision: 2,
  },
  {
    parameter: '单位耗电量（kWh/Nm3）',
    value: 4.90,
    unit: 'kWh/Nm3',
    category: '技术参数',
    remark: '制氢单位耗电量',
    description: '制氢过程中每立方米氢气的耗电量',
    min: 0,
    step: 0.1,
    precision: 2,
  },
  {
    parameter: '单位耗电量（kWh/kg）',
    value: 54.88,
    unit: 'kWh/kg',
    category: '技术参数',
    remark: '制氢单位耗电量',
    description: '制氢过程中每公斤氢气的耗电量',
    min: 0,
    step: 0.1,
    precision: 2,
  },
  {
    parameter: '设备使用年限（年）',
    value: 25.00,
    unit: '年',
    category: '基本参数',
    remark: '制氢设备使用年限',
    description: '制氢设备的设计使用年限',
    min: 10,
    max: 50,
    step: 1,
    precision: 2,
  },
  {
    parameter: '制氢设备年化衰减率',
    value: 0.01,
    unit: '',
    category: '技术参数',
    remark: '制氢设备年化衰减率',
    description: '制氢设备性能年化衰减率',
    min: 0,
    max: 0.1,
    step: 0.001,
    precision: 3,
  },
  {
    parameter: '水价（元/吨）',
    value: 8.60,
    unit: '元/吨',
    category: '经济参数',
    remark: '工业用水价格',
    description: '制氢过程中使用的工业用水价格',
    min: 0,
    step: 0.1,
    precision: 2,
  },
  {
    parameter: '排污水价（元/吨）',
    value: 40.00,
    unit: '元/吨',
    category: '经济参数',
    remark: '排污水处理费用',
    description: '制氢过程中产生的排污水处理费用',
    min: 0,
    step: 1,
    precision: 2,
  },
  {
    parameter: '人员数量（人)',
    value: 48.00,
    unit: '人',
    category: '基本参数',
    remark: '制氢厂人员数量',
    description: '制氢厂运营所需的人员数量',
    min: 0,
    step: 1,
    precision: 2,
  },
  {
    parameter: '人员工资（万元/年）',
    value: 10.00,
    unit: '万元/年',
    category: '经济参数',
    remark: '人员年均工资',
    description: '制氢厂人员的年均工资水平',
    min: 0,
    step: 0.5,
    precision: 2,
  },
  {
    parameter: '交通用氢价格（元/kg）',
    value: 20.00,
    unit: '元/kg',
    category: '经济参数',
    remark: '交通用氢销售价格',
    description: '氢气用于交通领域的销售价格',
    min: 0,
    step: 0.1,
    precision: 2,
  },
  {
    parameter: '化工用氢价格（元/kg）',
    value: 16.80,
    unit: '元/kg',
    category: '经济参数',
    remark: '化工用氢销售价格',
    description: '氢气用于化工领域的销售价格',
    min: 0,
    step: 0.1,
    precision: 2,
  },
  {
    parameter: '氧气价格（元/吨）',
    value: 200.00,
    unit: '元/吨',
    category: '经济参数',
    remark: '氧气销售价格',
    description: '制氢过程中产生的氧气销售价格',
    min: 0,
    step: 1,
    precision: 2,
  },
  {
    parameter: '设备折旧年限（年）',
    value: 25.00,
    unit: '年',
    category: '经济参数',
    remark: '设备折旧年限',
    description: '制氢设备的折旧年限',
    min: 10,
    max: 50,
    step: 1,
    precision: 2,
  },
  {
    parameter: '储氢投资（万元）',
    value: 0.00,
    unit: '万元',
    category: '经济参数',
    remark: '储氢设施投资',
    description: '储氢设施的投资成本',
    min: 0,
    step: 1,
    precision: 2,
  },
  {
    parameter: '储氢规模（吨）',
    value: 700.00,
    unit: '吨',
    category: '技术参数',
    remark: '储氢设施规模',
    description: '储氢设施的储存容量',
    min: 0,
    step: 10,
    precision: 2,
  },
]);

// 融资贷款模块数据
const financingData = ref([
  {
    parameter: '贷款年限（年）',
    value: 25.00,
    unit: '年',
    category: '经济参数',
    remark: '贷款期限',
    description: '项目融资贷款的期限',
    min: 1,
    max: 50,
    step: 1,
    precision: 2,
  },
  {
    parameter: '利率（%）',
    value: 0.05,
    unit: '%',
    category: '经济参数',
    remark: '贷款利率',
    description: '项目融资贷款的年利率',
    min: 0,
    max: 0.2,
    step: 0.001,
    precision: 3,
  },
  {
    parameter: '制氢厂土地租金（万元/年）',
    value: 10.00,
    unit: '万元/年',
    category: '经济参数',
    remark: '土地租金',
    description: '制氢厂用地的年租金',
    min: 0,
    step: 1,
    precision: 2,
  },
  {
    parameter: '贷款比例',
    value: 0.80,
    unit: '',
    category: '经济参数',
    remark: '贷款占总投资比例',
    description: '贷款金额占项目总投资的比例',
    min: 0,
    max: 1,
    step: 0.01,
    precision: 2,
  },
  {
    parameter: '贷款总额（万元）',
    value: 0,
    unit: '万元',
    category: '经济参数',
    remark: '贷款总额',
    description: '项目融资贷款的总金额',
    min: 0,
    step: 100,
    precision: 2,
  },
]);

// 液氨参数模块数据
const liquidAmmoniaData = ref([
  {
    parameter: '液氨价格（元/吨）',
    value: 3500,
    unit: '元/吨',
    category: '经济参数',
    remark: '液氨市场价格',
    description: '液氨的市场销售价格',
    min: 0,
    step: 10,
    precision: 0,
  },
  {
    parameter: '液氨用量（吨/小时）',
    value: 20,
    unit: '吨/小时',
    category: '技术参数',
    remark: '液氨生产用量',
    description: '液氨生产的小时用量',
    min: 0,
    step: 1,
    precision: 0,
  },
]);
const energyStoragePowerStationData = ref([
  {
    parameter: '电化学储能规模(MW)',
    value: 25,
    unit: 'MW',
    category: '技术参数',
    remark: '',
    description: '',
    min: 0,
    step: 1,
    precision: 0,
  }
])
const allModuleData = computed(() => ({
  photovoltaic: transformArrayToObject(photovoltaicData.value),
  windPower: transformArrayToObject(windPowerData.value),
  gridSystem: transformArrayToObject(gridSystemData.value),
  hydrogenPlant: transformArrayToObject(hydrogenPlantData.value),
  financing: transformArrayToObject(financingData.value),
  liquidAmmonia: transformArrayToObject(liquidAmmoniaData.value),
  energyStoragePowerStation: transformArrayToObject(energyStoragePowerStationData.value),
}));
// 计算已配置模块数量
const configuredModules = computed(() => {
  let count = 0;
  if (photovoltaicData.value.some((item) => item.value > 0)) count++;
  if (windPowerData.value.some((item) => item.value > 0)) count++;
  if (gridSystemData.value.some((item) => item.value > 0)) count++;
  if (hydrogenPlantData.value.some((item) => item.value > 0)) count++;
  if (financingData.value.some((item) => item.value > 0)) count++;
  if (liquidAmmoniaData.value.some((item) => item.value > 0)) count++;
  if (energyStoragePowerStationData.value.some((item) => item.value > 0)) count++;
  return count;
});

// 处理数据变化,可以用防抖来优化性能
const handleDataChange = (data) => {
  console.log('数据变化:', data);//来自某一个模块的数据，并非全部模块
  const {moduleTitle, index, row, moduleData} = data;
  
  switch (moduleTitle) {
    case '光伏参数':
      photovoltaicData.value = moduleData;
      break;
    case '风电参数':
      windPowerData.value = moduleData;
      break;
    case '电网系统':
      gridSystemData.value = moduleData;
      break;
    case '制氢厂参数':
      hydrogenPlantData.value = moduleData;
      break;
    case '融资贷款':
      financingData.value = moduleData;
      break;
    case '液氨参数':
      liquidAmmoniaData.value = moduleData;
      break;
    case '储能电站参数':
      energyStoragePowerStationData.value = moduleData;
      break;
    default:
      break;
  }
  
  // 实时更新Pinia
  projectStore.updateCurrentInputData('energy', allModuleData.value);
};
const handleCalculateAll = async () => {
  calculating.value = true;
  try {
    console.log('开始计算所有模块数据:', allModuleData.value);
    
    // 创建新的计算会话并保存数据到Pinia
    const sessionId = projectStore.createCalculationSession(allModuleData.value);
    
    // 调用后端API
    const result = await calculate(allModuleData.value);
    
    if (result.status === 200) {
      ElMessage.success('计算成功');
      console.log('计算结果:', result.data);
      console.log('当前会话ID:', sessionId);
      
      // 可以跳转到结果页面，并传递sessionId
      // router.push({ name: 'ResultsAnalysis', query: { sessionId } });
    } else {
      ElMessage.error('计算失败，请检查输入数据');
    }
  } catch (error) {
    console.error('计算过程出错:', error);
    ElMessage.error('计算过程出错，请重试');
  } finally {
    calculating.value = false;
  }
}
// Excel导入相关功能
const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel';
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件(.xlsx, .xls)');
    return false;
  }

  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB');
    return false;
  }

  return true;
};

const handleFileChange = (file) => {
  if (!file.raw) return;

  const reader = new FileReader();
  reader.onload = (e) => {
    try {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, {type: 'array'});

      // Excel文件中包含7个工作表对应7个模块
      const sheets = {
        光伏参数: workbook.Sheets['光伏参数'] || workbook.Sheets[workbook.SheetNames[0]],
        风电参数: workbook.Sheets['风电参数'] || workbook.Sheets[workbook.SheetNames[1]],
        电网系统: workbook.Sheets['电网系统'] || workbook.Sheets[workbook.SheetNames[2]],
        制氢厂参数: workbook.Sheets['制氢厂参数'] || workbook.Sheets[workbook.SheetNames[3]],
        融资贷款: workbook.Sheets['融资贷款'] || workbook.Sheets[workbook.SheetNames[4]],
        液氨参数: workbook.Sheets['液氨参数'] || workbook.Sheets[workbook.SheetNames[5]],
        储能电站参数: workbook.Sheets['储能电站参数'] || workbook.Sheets[workbook.SheetNames[6]],
      };

      processImportedData(sheets);
    } catch (error) {
      console.error('Excel解析失败:', error);
      ElMessage.error('Excel文件格式错误，请检查文件格式');
    }
  };
  reader.readAsArrayBuffer(file.raw);
};

const processImportedData = (sheets) => {
  const results = {
    missing: [],
    extra: [],
    updated: 0,
  };

  // 处理光伏参数数据
  if (sheets['光伏参数']) {
    const photovoltaicImported = parseSheetData(sheets['光伏参数']);
    console.log('光伏参数数据:', photovoltaicImported);
    const photovoltaicResult = updateModuleData(photovoltaicData.value, photovoltaicImported, '光伏参数');
    results.missing.push(...photovoltaicResult.missing);
    results.extra.push(...photovoltaicResult.extra);
    results.updated += photovoltaicResult.updated;
  }

  // 处理风电参数数据
  if (sheets['风电参数']) {
    const windPowerImported = parseSheetData(sheets['风电参数']);
    const windPowerResult = updateModuleData(windPowerData.value, windPowerImported, '风电参数');
    results.missing.push(...windPowerResult.missing);
    results.extra.push(...windPowerResult.extra);
    results.updated += windPowerResult.updated;
  }

  // 处理电网系统数据
  if (sheets['电网系统']) {
    const gridSystemImported = parseSheetData(sheets['电网系统']);
    const gridSystemResult = updateModuleData(gridSystemData.value, gridSystemImported, '电网系统');
    results.missing.push(...gridSystemResult.missing);
    results.extra.push(...gridSystemResult.extra);
    results.updated += gridSystemResult.updated;
  }

  // 处理制氢厂参数数据
  if (sheets['制氢厂参数']) {
    const hydrogenPlantImported = parseSheetData(sheets['制氢厂参数']);
    const hydrogenPlantResult = updateModuleData(hydrogenPlantData.value, hydrogenPlantImported, '制氢厂参数');
    results.missing.push(...hydrogenPlantResult.missing);
    results.extra.push(...hydrogenPlantResult.extra);
    results.updated += hydrogenPlantResult.updated;
  }

  // 处理融资贷款数据
  if (sheets['融资贷款']) {
    const financingImported = parseSheetData(sheets['融资贷款']);
    const financingResult = updateModuleData(financingData.value, financingImported, '融资贷款');
    results.missing.push(...financingResult.missing);
    results.extra.push(...financingResult.extra);
    results.updated += financingResult.updated;
  }

  // 处理液氨参数数据
  if (sheets['液氨参数']) {
    const liquidAmmoniaImported = parseSheetData(sheets['液氨参数']);
    const liquidAmmoniaResult = updateModuleData(liquidAmmoniaData.value, liquidAmmoniaImported, '液氨参数');
    results.missing.push(...liquidAmmoniaResult.missing);
    results.extra.push(...liquidAmmoniaResult.extra);
    results.updated += liquidAmmoniaResult.updated;
  }

  if (sheets['储能电站参数']) {
    const energyStoragePowerStationImported = parseSheetData(sheets['储能电站参数']);
    const energyStoragePowerStationResult = updateModuleData(energyStoragePowerStationData.value, energyStoragePowerStationImported, '储能电站参数');
    results.missing.push(...energyStoragePowerStationResult.missing);
    results.extra.push(...energyStoragePowerStationResult.extra);
    results.updated += energyStoragePowerStationResult.updated;
  }

  // 显示导入结果
  showImportResults(results);
};

const parseSheetData = (sheet) => {
  const data = {};
  const json = XLSX.utils.sheet_to_json(sheet, {header: 1});

  // 跳过表头，从第二行开始
  for (let i = 1; i < json.length; i++) {
    const row = json[i];
    if (row[0] && row[1] !== undefined) {
      // 参数名称和数值都存在
      data[row[0]] = row[1];
    }
  }

  return data;
};

const updateModuleData = (moduleData, importedData, moduleName) => {
  const result = {
    missing: [],
    extra: [],
    updated: 0,
  };

  const existingParams = new Set(moduleData.map((item) => item.parameter));
  const importedParams = new Set(Object.keys(importedData));

  // 检查缺失的参数
  existingParams.forEach((param) => {
    if (!importedParams.has(param)) {
      result.missing.push(`${moduleName} - ${param}`);
    }
  });

  // 检查多余的参数
  importedParams.forEach((param) => {
    if (!existingParams.has(param)) {
      result.extra.push(`${moduleName} - ${param}`);
    }
  });

  // 更新存在的参数
  moduleData.forEach((item) => {
    if (importedData.hasOwnProperty(item.parameter)) {
      const newValue = Number(importedData[item.parameter]);
      if (!isNaN(newValue)) {
        item.value = newValue;
        result.updated++;
      }
    }
  });

  return result;
};

const showImportResults = (results) => {
  let message = `成功导入 ${results.updated} 个参数`;

  if (results.missing.length > 0) {
    message += `\n\n缺失参数：\n${results.missing.join('\n')}`;
  }

  if (results.extra.length > 0) {
    message += `\n\n多余参数：\n${results.extra.join('\n')}`;
  }

  ElMessageBox.alert(message, '导入结果', {
    confirmButtonText: '确定',
    type: results.missing.length > 0 || results.extra.length > 0 ? 'warning' : 'success',
  });
};

const downloadTemplate = () => {
  try {
    // 创建工作簿
    const wb = XLSX.utils.book_new();

    // 创建光伏参数工作表
    const photovoltaicSheet = createTemplateSheet(photovoltaicData.value);
    XLSX.utils.book_append_sheet(wb, photovoltaicSheet, '光伏参数');

    // 创建风电参数工作表
    const windPowerSheet = createTemplateSheet(windPowerData.value);
    XLSX.utils.book_append_sheet(wb, windPowerSheet, '风电参数');

    // 创建电网系统工作表
    const gridSystemSheet = createTemplateSheet(gridSystemData.value);
    XLSX.utils.book_append_sheet(wb, gridSystemSheet, '电网系统');

    // 创建制氢厂参数工作表
    const hydrogenPlantSheet = createTemplateSheet(hydrogenPlantData.value);
    XLSX.utils.book_append_sheet(wb, hydrogenPlantSheet, '制氢厂参数');

    // 创建融资贷款工作表
    const financingSheet = createTemplateSheet(financingData.value);
    XLSX.utils.book_append_sheet(wb, financingSheet, '融资贷款');

    // 创建液氨参数工作表
    const liquidAmmoniaSheet = createTemplateSheet(liquidAmmoniaData.value);
    XLSX.utils.book_append_sheet(wb, liquidAmmoniaSheet, '液氨参数');

    //  创建储能电站工作表
    const energyStoragePowerStationSheet = createTemplateSheet(energyStoragePowerStationData.value);
    XLSX.utils.book_append_sheet(wb, energyStoragePowerStationSheet, '储能电站参数');
    // 导出文件
    const fileName = `能源项目经济测算参数模板_${new Date().toLocaleDateString().replace(/\//g, '-')}.xlsx`;
    XLSX.writeFile(wb, fileName);

    ElMessage.success('模板下载成功！');
  } catch (error) {
    console.error('模板下载失败:', error);
    ElMessage.error('模板下载失败，请重试');
  }
};

const createTemplateSheet = (data) => {
  const sheetData = [
    ['参数名称', '数值', '单位', '类别', '备注'],
    ...data.map((item) => [item.parameter, item.value, item.unit, item.category, item.remark]),
  ];

  const sheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 设置列宽
  sheet['!cols'] = [{wch: 20}, {wch: 15}, {wch: 10}, {wch: 15}, {wch: 30}];

  return sheet;
};

const handleReset = async () => {
  try {
    await ElMessageBox.confirm('确定要重置所有数据吗？此操作不可撤销。', '确认重置', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    // 重置为初始值
    location.reload();
  } catch {
    // 用户取消操作
  }
};
</script>

<style scoped>
.energy-calculation-page {
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.header-text {
  text-align: left;
}

.header-text h1 {
  color: #303133;
  margin-bottom: 10px;
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 10px 0;
}

.header-text p {
  color: #606266;
  font-size: 16px;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.calculation-actions {
  margin-top: 40px;
  position: sticky;
  bottom: 20px;
  z-index: 100;
}

.actions-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.summary-info h3 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 18px;
}

.summary-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
  }

  .header-text {
    text-align: center;
  }

  .header-actions {
    justify-content: center;
  }

  .actions-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .action-buttons {
    justify-content: center;
  }
}
</style>
