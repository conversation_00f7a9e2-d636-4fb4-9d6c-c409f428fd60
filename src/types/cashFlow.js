// 基础年度数据接口
/**
 * 基础年度数据
 * @typedef {Object} BaseAnnualData
 * @property {number} [id]
 * @property {number} project_id
 * @property {number} year - 年份标识：0=合计，负数表示建设期（-2,-1），正数表示运营期（1,2,3...25）
 * @property {string} [calculated_at]
 * @property {string} [created_at]
 * @property {string} [updated_at]
 */

/**
 * 现金流量表数据
 * @typedef {BaseAnnualData} CashFlowData
 * @property {number} h2_transport_sales_income - 销售氢气（交通）收入
 * @property {number} h2_chemical_sales_income - 销售氢气（化工）收入
 * @property {number} o2_sales_income - 销售氧气（化工）收入
 * @property {number} ammonia_sales_income - 销售液氨（化工）收入
 * @property {number} steam_sales_income - 销售蒸汽（化工）收入
 * @property {number} pv_electricity_sales_income - 光伏售电收入
 * @property {number} wind_electricity_sales_income - 风电售电收入
 * @property {number} other_income - 其他收入
 * @property {number} pv_station_residual_value - 光伏电站残值（5%）
 * @property {number} wind_station_residual_value - 风电站残值（5%）
 * @property {number} pv_transmission_residual_value - 光伏送出线路残值（5%）
 * @property {number} wind_transmission_residual_value - 风电送出线路残值（5%）
 * @property {number} step_up_station_residual_value - 升压站残值（5%）
 * @property {number} step_down_station_residual_value - 降压站残值（5%）
 * @property {number} battery_storage_residual_value - 电池储能系统残值（5%）
 * @property {number} h2_plant_residual_value - 制氢工厂残值（5%）
 * @property {number} h2_storage_residual_value - 储氢系统残值（5%）
 * @property {number} h2_auxiliary_residual_value - 制氢公辅设施残值（5%）
 * @property {number} synthesis_equipment_residual_value - 合成设备残值（5%）
 * @property {number} other_equipment_residual_value - 其他设备残值（5%）
 * @property {number} government_subsidy - 政府直接补贴
 * @property {number} total_cash_inflow - 现金流入合计
 * @property {number} pv_station_investment - 光伏电站投资
 * @property {number} wind_station_investment - 风电站投资
 * @property {number} pv_transmission_investment - 光伏送出线路投资
 * @property {number} wind_transmission_investment - 风电送出线路投资
 * @property {number} step_up_station_investment - 升压站投资
 * @property {number} step_down_station_investment - 降压站投资
 * @property {number} battery_storage_investment - 电池储能系统投资
 * @property {number} h2_plant_investment - 制氢工厂投资
 * @property {number} h2_storage_investment - 储氢系统投资
 * @property {number} h2_auxiliary_investment - 制氢公辅设施投资
 * @property {number} synthesis_equipment_investment - 合成设备投资
 * @property {number} other_equipment_investment - 其他设备投资
 * @property {number} h2_raw_materials_cost - 制氢站外购原材料（化学品）
 * @property {number} h2_maintenance_cost - 制氢站维修费用（2%）
 * @property {number} h2_personnel_cost - 制氢站人员支出
 * @property {number} h2_other_operating_cost - 制氢站其他运营管理支出
 * @property {number} h2_water_cost - 制氢水费
 * @property {number} h2_wastewater_cost - 制氢排污水费
 * @property {number} h2_insurance_cost - 制氢站保险
 * @property {number} pv_operating_cost - 光伏运营、管理成本
 * @property {number} pv_insurance_cost - 光伏保险费用
 * @property {number} pv_financial_cost - 光伏财务成本
 * @property {number} wind_operating_cost - 风电运营、管理成本
 * @property {number} wind_insurance_cost - 风电保险费用
 * @property {number} wind_financial_cost - 风电财务成本
 * @property {number} total_cash_outflow - 现金流出合计
 * @property {number} net_cash_flow_before_tax - 所得税前净现金流量
 * @property {number} cumulative_net_cash_flow_before_tax - 所得税前累计净现金流量
 * @property {number} depreciation - 折旧
 * @property {number} vat_output - 增值税销项（售氢售氧补贴按13%）
 * @property {number} vat_input_operating - 增值税进项（一）（运行成本按13%）
 * @property {number} vat_input_fixed_assets - 增值税进项（二）（固定资产按9%）
 * @property {number} vat_payable - 缴纳增值税
 * @property {number} vat_surcharge - 缴纳增值税附加
 * @property {number} total_vat_and_surcharge - 增值税及附加总额
 * @property {number} income_tax - 所得税
 * @property {number} net_cash_flow_after_tax - 税后净现金流
 * @property {number} cumulative_net_cash_flow_after_tax - 税后累计现金流
 */

/**
 * 现金流量表显示数据（用于前端表格显示）
 * @typedef {Object} CashFlowTableRow
 * @property {string} 项目名称
 * @property {string} 合计
 * @property {string} '-2' - 建设期第2年
 * @property {string} '-1' - 建设期第1年
 * @property {string|boolean|undefined} [key] - 支持动态年份键和其他属性
 * @property {boolean} [isParent] - 是否为父级行
 * @property {boolean} [isChild] - 是否为子级行
 */

/**
 * 现金流量表类型
 * @enum {string}
 */
export const CashFlowType = {
  CASH_INFLOW: 'cashInflow',
  CASH_OUTFLOW: 'cashOutflow',
  NET_CASH_FLOW: 'netCashFlow'
}

/**
 * 现金流量表配置
 * @typedef {Object} CashFlowConfig
 * @property {number} projectId
 * @property {number[]} years - 年份数组，包含建设期和运营期
 * @property {CashFlowType} type
 */

/**
 * 现金流量表API响应
 * @typedef {Object} CashFlowResponse
 * @property {boolean} success
 * @property {number} code
 * @property {string} message
 * @property {CashFlowData[]} data
 */

/**
 * 财务配置
 * @typedef {Object} FinancialConfig
 * @property {number} constructionYears
 * @property {number} operationYears
 */

/**
 * 财务指标
 * @typedef {Object} FinancialIndicators
 * @property {number} npv
 * @property {number} irr
 * @property {number} paybackPeriod
 * @property {number} profitabilityIndex
 */

/**
 * 通用API响应
 * @template T
 * @typedef {Object} ApiResponse
 * @property {number} code
 * @property {T} data
 * @property {string} message
 */

/**
 * 导出参数
 * @typedef {Object} ExportParams
 * @property {number} [projectId]
 * @property {('excel'|'pdf')} [format]
 * @property {boolean} [includeCharts]
 */

/**
 * 计算参数
 * @typedef {Object} CalculationParams
 * @property {number} projectId
 * @property {number} [discountRate]
 * @property {number} [taxRate]
 */

/**
 * 表格列配置
 * @typedef {Object} TableColumn
 * @property {string} prop
 * @property {string} label
 * @property {number} [width]
 * @property {string} [align]
 * @property {string} [headerAlign]
 */

export {}
