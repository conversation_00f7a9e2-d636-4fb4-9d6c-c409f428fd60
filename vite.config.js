import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path' // 新增路径处理模块

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // 根据模式设置代理目标
  const proxyTarget = mode === 'development'
    ? 'http://************:8000'
    : 'http://localhost:8000'

  return {
    plugins: [vue()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src') // 配置@指向src目录
      },
      extensions: [".js", ".ts", ".jsx", ".tsx", ".json"],
    },
    server: {
      proxy: {
        '/api': {
          target: proxyTarget,
          changeOrigin: true,
        }
      }
    },

    build: {
      outDir: "dist",
      minify: "terser", // 使用 terser 压缩代码
    },
  }
})